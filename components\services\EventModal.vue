<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-neutral-200">
        <h2 class="text-xl font-semibold text-neutral-900">
          {{ event ? 'Edit Event' : 'Create New Event' }}
        </h2>
        <button 
          @click="$emit('close')"
          class="p-2 text-neutral-400 hover:text-neutral-600 hover:bg-neutral-100 rounded-lg transition-colors"
        >
          <Icon name="lucide:x" class="w-5 h-5" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Title -->
        <div>
          <label for="title" class="block text-sm font-medium text-neutral-700 mb-2">
            Event Title *
          </label>
          <input
            id="title"
            v-model="form.title"
            type="text"
            required
            class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Enter event title"
          />
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-neutral-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            v-model="form.description"
            required
            rows="3"
            class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Describe your event"
          ></textarea>
        </div>

        <!-- Date Range -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="startDate" class="block text-sm font-medium text-neutral-700 mb-2">
              Start Date *
            </label>
            <input
              id="startDate"
              v-model="form.startDate"
              type="date"
              required
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label for="endDate" class="block text-sm font-medium text-neutral-700 mb-2">
              End Date *
            </label>
            <input
              id="endDate"
              v-model="form.endDate"
              type="date"
              required
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>

        <!-- Time Range -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="startTime" class="block text-sm font-medium text-neutral-700 mb-2">
              Start Time *
            </label>
            <input
              id="startTime"
              v-model="form.startTime"
              type="time"
              required
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label for="endTime" class="block text-sm font-medium text-neutral-700 mb-2">
              End Time *
            </label>
            <input
              id="endTime"
              v-model="form.endTime"
              type="time"
              required
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>

        <!-- Location and Category -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="location" class="block text-sm font-medium text-neutral-700 mb-2">
              Location
            </label>
            <input
              id="location"
              v-model="form.location"
              type="text"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Event location"
            />
          </div>
          <div>
            <label for="category" class="block text-sm font-medium text-neutral-700 mb-2">
              Category
            </label>
            <input
              id="category"
              v-model="form.category"
              type="text"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Event category"
            />
          </div>
        </div>

        <!-- Max Attendees and Price -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="maxAttendees" class="block text-sm font-medium text-neutral-700 mb-2">
              Max Attendees
            </label>
            <input
              id="maxAttendees"
              v-model.number="form.maxAttendees"
              type="number"
              min="1"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Maximum number of attendees"
            />
          </div>
          <div>
            <label for="price" class="block text-sm font-medium text-neutral-700 mb-2">
              Price ($)
            </label>
            <input
              id="price"
              v-model.number="form.price"
              type="number"
              min="0"
              step="0.01"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="0.00"
            />
          </div>
        </div>

        <!-- Image Upload -->
        <div>
          <label class="block text-sm font-medium text-neutral-700 mb-2">
            Event Image
          </label>
          <ImageUpload
            v-model="form.imageUrl"
            @upload="handleImageUpload"
            :loading="imageUploading"
            alt-text="Event image"
            upload-text="Upload event image"
            upload-subtext="Add an image to showcase your event"
          />
        </div>

        <!-- Active Status -->
        <div class="flex items-center">
          <input
            id="isActive"
            v-model="form.isActive"
            type="checkbox"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
          />
          <label for="isActive" class="ml-2 block text-sm text-neutral-700">
            Event is active and visible to customers
          </label>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-neutral-200">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-neutral-700 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading">Saving...</span>
            <span v-else>{{ event ? 'Update Event' : 'Create Event' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Event, EventFormData } from '~/types/service.types'
import ImageUpload from '~/components/ui/ImageUpload.vue'

interface Props {
  event?: Event | null
}

interface Emits {
  close: []
  save: [data: EventFormData, imageFile?: File | null]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const imageUploading = ref(false)
const selectedImageFile = ref<File | null>(null)

// Form data
const form = reactive<EventFormData>({
  title: '',
  description: '',
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  location: '',
  category: '',
  isActive: true,
  maxAttendees: undefined,
  price: undefined,
  imageUrl: ''
})

// Initialize form with event data if editing
watchEffect(() => {
  if (props.event) {
    Object.assign(form, {
      title: props.event.title,
      description: props.event.description,
      startDate: props.event.startDate.split('T')[0], // Extract date part
      endDate: props.event.endDate.split('T')[0],
      startTime: props.event.startTime,
      endTime: props.event.endTime,
      location: props.event.location || '',
      category: props.event.category || '',
      isActive: props.event.isActive,
      maxAttendees: props.event.maxAttendees,
      price: props.event.price,
      imageUrl: props.event.imageUrl || ''
    })
  }
})

// Handle image upload
const handleImageUpload = async (file: File) => {
  selectedImageFile.value = file
  // Store the file for later use when saving the event
  // The actual upload will happen when the form is submitted
}

const handleSubmit = async () => {
  loading.value = true
  try {
    emit('save', { ...form }, selectedImageFile.value)
  } finally {
    loading.value = false
  }
}
</script>
