<template>
  <div class="space-y-4">
    <!-- Current Image Preview -->
    <div v-if="modelValue" class="relative">
      <img
        :src="modelValue"
        :alt="altText"
        :class="previewImageClass"
      />
      <Button
        @click="removeImage"
        variant="destructive"
        size="sm"
        class="absolute top-2 right-2 bg-red-600 text-white hover:bg-red-700"
      >
        <Icon name="lucide:x" class="w-4 h-4" />
      </Button>
    </div>

    <!-- Upload Area -->
    <div
      ref="dropZone"
      @click="triggerFileInput"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      class="relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200"
      :class="[
        isDragOver
          ? 'border-primary-500 bg-primary-50'
          : 'border-neutral-300 hover:border-primary-400 hover:bg-neutral-50',
        loading ? 'pointer-events-none opacity-50' : ''
      ]"
    >
      <!-- Loading State -->
      <div v-if="loading" class="flex flex-col items-center space-y-2">
        <Icon name="lucide:loader-2" class="w-8 h-8 text-primary-600 animate-spin" />
        <p class="text-sm text-neutral-600">Uploading image...</p>
      </div>

      <!-- Upload State -->
      <div v-else class="flex flex-col items-center space-y-2">
        <Icon name="lucide:upload" class="w-8 h-8 text-neutral-400" />
        <div class="space-y-1">
          <p class="text-sm font-medium text-neutral-900">
            {{ uploadText }}
          </p>
          <p class="text-xs text-neutral-500">
            {{ uploadSubtextComputed }}
          </p>
        </div>
      </div>

      <!-- Hidden File Input -->
      <input
        ref="fileInput"
        type="file"
        :accept="acceptString"
        class="hidden"
        @change="handleFileSelect"
      />
    </div>

    <!-- Upload Progress -->
    <div v-if="uploadProgress > 0 && uploadProgress < 100" class="w-full">
      <div class="flex justify-between text-sm text-neutral-600 mb-1">
        <span>Uploading...</span>
        <span>{{ uploadProgress }}%</span>
      </div>
      <div class="w-full bg-neutral-200 rounded-full h-2">
        <div
          class="bg-primary-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${uploadProgress}%` }"
        ></div>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue?: string
  loading?: boolean
  altText?: string
  previewHeight?: string
  maxFileSize?: number // in MB
  acceptedTypes?: string[]
  uploadText?: string
  uploadSubtext?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'upload', file: File): void
}

const props = withDefaults(defineProps<Props>(), {
  altText: 'Uploaded image',
  previewHeight: 'h-48',
  maxFileSize: 10,
  acceptedTypes: () => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  uploadText: 'Click to upload or drag and drop',
  uploadSubtext: 'PNG, JPG, GIF up to 10MB'
})
const emit = defineEmits<Emits>()

// Refs
const dropZone = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()

// State
const isDragOver = ref(false)
const uploadProgress = ref(0)
const error = ref('')

// Computed properties
const previewImageClass = computed(() => [
  'w-full object-cover rounded-lg border border-neutral-200',
  props.previewHeight
])

const acceptString = computed(() => props.acceptedTypes.join(','))

const maxSizeBytes = computed(() => props.maxFileSize * 1024 * 1024)

const uploadSubtextComputed = computed(() => {
  const types = props.acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')
  return `${types} up to ${props.maxFileSize}MB`
})

// File validation
const validateFile = (file: File): string | null => {
  if (!props.acceptedTypes.includes(file.type)) {
    const types = props.acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')
    return `Please select a valid image file (${types})`
  }

  if (file.size > maxSizeBytes.value) {
    return `File size must be less than ${props.maxFileSize}MB`
  }

  return null
}

// Handle file selection
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    processFile(file)
  }
}

// Handle drag and drop
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

// Process selected file
const processFile = (file: File) => {
  error.value = ''
  
  const validationError = validateFile(file)
  if (validationError) {
    error.value = validationError
    return
  }

  // Create preview
  const reader = new FileReader()
  reader.onload = (e) => {
    emit('update:modelValue', e.target?.result as string)
  }
  reader.readAsDataURL(file)

  // Emit file for upload
  emit('upload', file)
}

// Trigger file input
const triggerFileInput = () => {
  if (!props.loading) {
    fileInput.value?.click()
  }
}

// Remove image
const removeImage = () => {
  emit('update:modelValue', '')
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// Simulate upload progress (replace with actual progress tracking)
watch(() => props.loading, (isLoading) => {
  if (isLoading) {
    uploadProgress.value = 0
    const interval = setInterval(() => {
      uploadProgress.value += 10
      if (uploadProgress.value >= 100) {
        clearInterval(interval)
        setTimeout(() => {
          uploadProgress.value = 0
        }, 500)
      }
    }, 200)
  }
})
</script>
