import { FetchError } from "ofetch";

type FetchOptions = Record<string, any>;

// This composable provides a reusable API fetch function for POST requests
// It uses the Nuxt $fetch function to handle API requests
// It includes error handling and authentication management
export const $apiFetch = async <
  T = unknown,
  B = any
>(
  url: string, // The endpoint URL to fetch data from
  body?: B, // The body of the request, can be any type
  options: FetchOptions = {} // Additional options for the fetch request
): Promise<T> => {
  const nuxtApp = useNuxtApp();
  const token = nuxtApp.$auth.value.token;
  const config = useRuntimeConfig();

  // This sets the headers for the fetch request
  // It includes the Authorization header if a token is present
  // and sets the Accept header to application/json
  let headers: Record<string, string> = {
    Accept: "application/json",
    authorization: token ? `Bearer ${token}` : "",
  };

  // This checks if the body is a FormData object
  // If it is, it does not set the Content-Type header
  // Otherwise, it sets the Content-Type to application/json
  let fetchOptions: FetchOptions = {
    method: "POST",
    headers,
    ...options,
  };

  if (body) {
    if (body instanceof FormData) {
      fetchOptions.body = body as any;
    } else {
      fetchOptions.body = JSON.stringify(body);
      (fetchOptions.headers as Record<string, string>)["Content-Type"] = "application/json";
    }
  }

  try {
    return await $fetch(`${config.public.apiBase}${url}`, fetchOptions); // This performs the fetch request to the API
  } catch (error) {
    if (error instanceof FetchError) {
      if (error.status === 401) {
        // nuxtApp.$logout();
      }
    }
    throw error;
  }
};