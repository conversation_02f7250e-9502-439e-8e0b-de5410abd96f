export interface Service {
  id: string
  name: string
  description: string
  price: number
  duration: number // in minutes
  imageUrl?: string
  categoryId?: string
  category?: ServiceCategory // For populated category data
  isActive: boolean
  allowOverlap: boolean // New field for overlap functionality
  // availableTimes?: TimeSlot[] // Available time slots for booking
  createdAt: string
  updatedAt: string
}

export interface ServiceFormData {
  name: string
  description: string
  price: number
  duration: number
  imageUrl?: string
  categoryId?: string
  isActive: boolean
  allowOverlap: boolean
  // availableTimes?: TimeSlot[]
}

export interface ServiceCategory {
  id: string
  name: string
  description?: string
}

// Time slot interface for service booking
// export interface TimeSlot {
//   id: string
//   startTime: string // Format: "HH:mm" (e.g., "09:00")
//   endTime: string   // Format: "HH:mm" (e.g., "17:00")
//   dayOfWeek: number // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
//   isAvailable: boolean
// }

// Event types for the events/services toggle functionality
export interface Event {
  id: string
  title: string
  description: string
  startDate: string // ISO date string
  endDate: string   // ISO date string
  startTime: string // Format: "HH:mm"
  endTime: string   // Format: "HH:mm"
  location?: string
  category?: string
  isActive: boolean
  maxAttendees?: number
  currentAttendees: number
  price?: number
  imageUrl?: string
  createdAt: string
  updatedAt: string
}

export interface EventFormData {
  title: string
  description: string
  startDate: string
  endDate: string
  startTime: string
  endTime: string
  location?: string
  category?: string
  isActive: boolean
  maxAttendees?: number
  price?: number
  imageUrl?: string
}

// Booking related types
// export interface BookingTimeSlot {
//   date: string // ISO date string (e.g., "2024-01-15")
//   time: string // Format: "HH:mm" (e.g., "14:30")
//   duration: number // in minutes
//   isAvailable: boolean
// }

// export interface ServiceBooking {
//   id: string
//   serviceId: string
//   clientName: string
//   clientEmail: string
//   clientPhone?: string
//   bookingDate: string // ISO date string
//   bookingTime: string // Format: "HH:mm"
//   duration: number // in minutes
//   status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
//   notes?: string
//   createdAt: string
//   updatedAt: string
// }