<template>
  <div class="max-w-4xl mx-auto space-y-8">
      <!-- Page Header -->
      <profile-header />
      
      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div class="lg:col-span-2 space-y-6">
          <profile-info-card ref="infoCard" :show-actions="false" />

          <div class="flex justify-end">
            <button
              @click="handleSave"
              :disabled="updating"
              class="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <span v-if="updating" class="flex items-center">
                <Icon name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </span>
              <span v-else>Save Changes</span>
            </button>
          </div>
        </div>

        <div class="space-y-6">
          <profile-account-status-card />
          <profile-quick-actions-card @action="handleQuickAction" />
        </div>
      </div>
    </div>
</template>


<script setup lang="ts">
import { ref } from 'vue'

definePageMeta({
  title: 'Profile',
  description: 'Manage your profile information and preferences',
  layout: 'dashboard'
})

// Use centralized profile composable
const { updating } = useProfile()

// Ref to profile info card
const infoCard = ref<{ submit: () => Promise<void> } | null>(null)

const handleSave = async () => {
  try {
    // Trigger profile info card submit
    await infoCard.value?.submit()
  } catch (error) {
    // Error handling is done in the composable
    console.error('Failed to save profile:', error)
  }
}

const handleQuickAction = (action: string) => {
  switch (action) {
    case 'change-password':
      // Navigate to password change page or show modal
      // For now, show a toast indicating the feature
      $toast('Password change functionality will be implemented in a dedicated page', { type: 'info' })
      break
    case 'upgrade':
      // Handle upgrade action
      $toast('Upgrade functionality coming soon', { type: 'info' })
      break
    default:
      console.log('Unhandled quick action:', action)
  }
}
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>