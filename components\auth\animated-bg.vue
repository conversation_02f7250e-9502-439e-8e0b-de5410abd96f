<template>
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"
    ></div>
    <div
      class="absolute top-32 right-20 w-12 h-12 bg-white/15 rounded-full blur-lg animate-pulse"
      style="animation-delay: 1s"
    ></div>
    <div
      class="absolute bottom-20 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-xl animate-pulse"
      style="animation-delay: 2s"
    ></div>
    <div
      class="absolute bottom-32 right-10 w-8 h-8 bg-white/20 rounded-full blur-md animate-pulse"
      style="animation-delay: 0.5s"
    ></div>
    <div
      class="absolute top-1/2 left-20 w-6 h-6 bg-white/25 rounded-full blur-sm animate-pulse"
      style="animation-delay: 1.5s"
    ></div>
  </div>

  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute -top-40 -right-40 w-80 h-80 primary-background rounded-full blur-3xl opacity-20 animate-pulse"
    ></div>
    <div
      class="absolute -bottom-40 -left-40 w-80 h-80 secondary-background rounded-full blur-3xl opacity-20 animate-pulse"
      style="animation-delay: 1s"
    ></div>
    <div
      class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 tertiary-background rounded-full blur-3xl opacity-10 animate-pulse"
      style="animation-delay: 2s"
    ></div>
  </div>
</template>
