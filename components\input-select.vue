<template>
  <div :class="['input-field flex flex-col gap-1 w-full', $attrs.class]">
    <label
      v-if="label"
      class="block text-body-3 font-medium text-neutral-700 mb-2 transition-colors"
      :class="{ 'text-primary-600': isFocused }"
    >
      {{ label }}
      <span v-if="required && showRequired" class="text-primary-200 text-body-5 ml-1.5">⁕</span>
    </label>

    <div
      class="relative flex items-center w-full"
      @focusin="isFocused = true"
      @focusout="isFocused = false"
    >
      <slot name="left-slot" />
      <select
        :value="modelValue"
        @change="onChange"
        :disabled="disabled"
        class="w-full px-4 py-4 bg-neutral-50 border border-neutral-200 rounded-xl text-body-2 placeholder-neutral-400 transition-all duration-300 appearance-none"
        :class="{ 'border-primary-500 bg-white focus:ring-4 focus:ring-primary-100': isFocused }"
      >
        <option value="" disabled hidden v-if="placeholder">{{ placeholder }}</option>
        <option
          v-for="opt in options"
          :key="opt.id"
          :value="opt.id"
        >
          {{ opt.label }}
        </option>
      </select>

      <div class="absolute right-4 pointer-events-none">
        <svg class="w-4 h-4 text-neutral-500" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M6 9l6 6 6-6" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/></svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = withDefaults(defineProps<{
  modelValue: string
  label?: string
  required?: boolean
  showRequired?: boolean
  placeholder?: string
  disabled?: boolean
  options?: Array<{ id: string; label: string }>
}>(), {
  required: false,
  showRequired: true,
  placeholder: "Select...",
  disabled: false,
  label: "",
  options: [],
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const isFocused = ref(false)

function onChange(e: Event) {
  const target = e.target as HTMLSelectElement
  emit('update:modelValue', target.value)
}
</script>
