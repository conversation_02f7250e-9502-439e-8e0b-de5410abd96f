<template>
  <Dialog :open="show" @update:open="(open) => !open && $emit('cancel')">
    <DialogContent class="max-w-md">
      <DialogHeader class="text-center">
        <div class="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mx-auto mb-4">
          <Icon name="lucide:trash-2" class="w-6 h-6 text-red-600" />
        </div>
        <DialogTitle class="text-lg font-semibold text-neutral-900">
          {{ title || `Delete ${itemType}` }}
        </DialogTitle>
        <DialogDescription class="text-sm text-neutral-600 mt-2">
          {{ message || `Are you sure you want to delete this ${itemType.toLowerCase()}? This action cannot be undone.` }}
        </DialogDescription>
      </DialogHeader>

      <!-- Item Details -->
      <div v-if="itemName" class="mt-4 p-3 bg-neutral-50 rounded-lg border">
        <p class="text-sm font-medium text-neutral-900">{{ itemName }}</p>
        <p v-if="itemDescription" class="text-xs text-neutral-500 mt-1 line-clamp-2">
          {{ itemDescription }}
        </p>
      </div>

      <!-- Actions -->
      <DialogFooter class="flex flex-col sm:flex-row gap-3">
        <Button
          @click="$emit('cancel')"
          variant="outline"
          class="flex-1 order-2 sm:order-1"
          :disabled="loading"
        >
          Cancel
        </Button>
        <Button
          @click="$emit('confirm')"
          variant="destructive"
          class="flex-1 order-1 sm:order-2 bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"
          :disabled="loading"
        >
          <Icon v-if="loading" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
          {{ confirmText || 'Delete' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

interface Props {
  show: boolean
  title?: string
  message?: string
  itemType: string
  itemName?: string
  itemDescription?: string
  confirmText?: string
  loading?: boolean
}

interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// Handle escape key
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show && !props.loading) {
    emit('cancel')
  }
}

// Add/remove event listener
watchEffect((onInvalidate) => {
  if (props.show) {
    document.addEventListener('keydown', handleKeydown)
    document.body.style.overflow = 'hidden'

    onInvalidate(() => {
      document.removeEventListener('keydown', handleKeydown)
      document.body.style.overflow = ''
    })
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (typeof document !== 'undefined') {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
@keyframes animate-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes animate-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

.animate-in {
  animation: animate-in 0.2s ease-out;
}

.animate-out {
  animation: animate-out 0.15s ease-in;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
