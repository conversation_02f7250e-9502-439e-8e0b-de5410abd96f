<template>
  <div
    class="min-h-screen bg-gradient-to-br from-background via-neutral-150 to-neutral-200 relative overflow-hidden"
  >
    <div class="">
      <auth-animated-bg />
      <auth-floating-shapes />
    </div>

    <div class="relative z-10 min-h-screen flex flex-col lg:flex-row">
      <div
        class="lg:flex-1 lg:primary-background lg:relative lg:overflow-hidden lg:flex lg:items-center lg:justify-center p-4 lg:p-0"
      >
        <!-- Left side content similar to login page -->
        <div class="hidden lg:block text-center text-white">
          <div
            class="inline-flex items-center justify-center w-24 h-24 bg-white/10 border border-white/20 rounded-3xl mb-8"
          >
            <Icon name="lucide:mail-check" class="w-12 h-12" />
          </div>
          <h2 class="text-h3 font-bold mb-4">
            Check Your Email
          </h2>
          <p class="text-body-1 text-white/80 max-w-md mx-auto">
            We've sent a verification link to your email address. Please check your inbox and follow the instructions to complete your registration.
          </p>
        </div>

        <!-- Mobile: Header -->
        <div class="lg:hidden text-center animate-fade-in-up">
          <div
            class="inline-flex items-center justify-center w-24 h-24 bg-primary-900 border border-primary-900 rounded-3xl mb-2"
          >
            <Icon name="lucide:mail-check" class="w-12 h-12 text-white" />
          </div>
          <p
            class="text-body-2 text-neutral-600 animate-fade-in"
            style="animation-delay: 0.3s"
          >
            Verify your email to continue
          </p>
        </div>
      </div>

      <div
        class="lg:flex-1 lg:bg-white lg:relative lg:flex lg:items-center lg:justify-center flex-1 flex items-center justify-center p-4"
      >
        <div class="w-full max-w-md">
          <!-- Desktop Header -->
          <div class="hidden lg:block text-center mb-8">
            <div
              class="inline-flex items-center justify-center w-16 h-16 bg-neutral-100 rounded-2xl mb-6"
            >
              <Icon name="lucide:mail-check" class="w-8 h-8 text-primary-600" />
            </div>
            <h2 class="text-h4 font-bold text-neutral-900 mb-2">
              Verify Your Email
            </h2>
            <p class="text-body-2 text-neutral-600">
              We sent a verification email to <strong>{{ userEmail }}</strong>
            </p>
          </div>

          <div
            class="lg:bg-transparent lg:backdrop-blur-none lg:shadow-none lg:border-none lg:p-0 bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 animate-fade-in-up"
            style="animation-delay: 0.5s"
          >
            <!-- Verification Options -->
            <div class="space-y-6">
              <!-- OTP Verification -->
              <div v-if="verificationMethod === 'otp'" class="space-y-4">
                <div class="text-center mb-6">
                  <h3 class="text-lg font-semibold text-neutral-900 mb-2">
                    Enter Verification Code
                  </h3>
                  <p class="text-sm text-neutral-600">
                    Enter the 6-digit code sent to your email
                  </p>
                </div>

                <!-- OTP Input -->
                <div class="flex justify-center space-x-2">
                  <input
                    v-for="(digit, index) in otpDigits"
                    :key="index"
                    v-model="otpDigits[index]"
                    type="text"
                    maxlength="1"
                    class="w-12 h-12 text-center text-lg font-semibold border border-neutral-200 rounded-lg focus:border-primary-500 focus:ring-2 focus:ring-primary-100"
                    @input="onOtpInput(index, $event)"
                    @keydown="onOtpKeydown(index, $event)"
                  />
                </div>

                <!-- Verify Button -->
                <button
                  @click="verifyOtp"
                  :disabled="!isOtpComplete || verifying"
                  class="w-full py-4 primary-background text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="!verifying">Verify Email</span>
                  <span v-else class="flex items-center justify-center space-x-2">
                    <Icon name="lucide:loader-2" class="w-5 h-5 animate-spin" />
                    <span>Verifying...</span>
                  </span>
                </button>
              </div>

              <!-- Button Verification (Default) -->
              <div v-else class="space-y-4">
                <div class="text-center mb-6">
                  <Icon name="lucide:mail" class="w-16 h-16 text-primary-500 mx-auto mb-4" />
                  <h3 class="text-lg font-semibold text-neutral-900 mb-2">
                    Check Your Email
                  </h3>
                  <p class="text-sm text-neutral-600">
                    Click the verification link in the email we sent to complete your registration.
                  </p>
                </div>

                <!-- Manual Verification Button (for testing) -->
                <button
                  @click="manualVerify"
                  :disabled="verifying"
                  class="w-full py-4 primary-background text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="!verifying">I've Verified My Email</span>
                  <span v-else class="flex items-center justify-center space-x-2">
                    <Icon name="lucide:loader-2" class="w-5 h-5 animate-spin" />
                    <span>Verifying...</span>
                  </span>
                </button>
              </div>

              <!-- Resend Email -->
              <div class="text-center">
                <p class="text-sm text-neutral-600 mb-2">
                  Didn't receive the email?
                </p>
                <button
                  @click="resendEmail"
                  :disabled="resendCooldown > 0"
                  class="text-primary-600 hover:text-primary-700 font-medium transition-colors disabled:opacity-50"
                >
                  {{ resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Email' }}
                </button>
              </div>

              <!-- Switch Verification Method -->
              <div class="text-center pt-4 border-t border-neutral-200">
                <button
                  @click="switchVerificationMethod"
                  class="text-sm text-neutral-600 hover:text-primary-600 transition-colors"
                >
                  {{ verificationMethod === 'otp' ? 'Use email link instead' : 'Use verification code instead' }}
                </button>
              </div>

              <!-- Back to Login -->
              <div class="text-center">
                <NuxtLink
                  to="/login"
                  class="text-sm text-neutral-600 hover:text-primary-600 transition-colors"
                >
                  ← Back to Login
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Meta tags
useHead({
  title: 'Verify Email - Bookiime',
  meta: [
    { name: 'description', content: 'Verify your email address to complete registration' }
  ]
})

// Check if user should be on this page
const verificationToken = useCookie<string | null>('verification_token', {
  default: () => null,
  secure: true,
  sameSite: 'strict'
})

const userEmail = useCookie<string>('verification_email', {
  default: () => '',
  secure: true,
  sameSite: 'strict'
})

// Redirect if no verification token
if (!verificationToken.value) {
  await navigateTo('/login')
}

const verificationMethod = ref<'otp' | 'link'>('link')
const verifying = ref(false)
const resendCooldown = ref(0)

// OTP related
const otpDigits = ref(['', '', '', '', '', ''])
const isOtpComplete = computed(() => otpDigits.value.every(digit => digit !== ''))

const onOtpInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '') // Only allow digits
  
  if (value) {
    otpDigits.value[index] = value
    // Auto-focus next input
    if (index < 5) {
      const nextInput = target.parentElement?.children[index + 1] as HTMLInputElement
      nextInput?.focus()
    }
  }
}

const onOtpKeydown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0) {
    // Focus previous input on backspace
    const prevInput = (event.target as HTMLInputElement).parentElement?.children[index - 1] as HTMLInputElement
    prevInput?.focus()
  }
}

const verifyOtp = async () => {
  const otp = otpDigits.value.join('')
  verifying.value = true
  
  try {
    // Replace with actual API call
    const response = await $apiFetch<{ success: boolean, access_token?: string }>(
      '/auth/verify-email-otp',
      { 
        token: verificationToken.value,
        otp 
      }
    )
    
    if (response.success) {
      $toast('Email verified successfully!', { type: 'success' })
      
      // Clear verification tokens
      verificationToken.value = null
      userEmail.value = ''
      
      // If access token provided, log user in
      if (response.access_token) {
        const { $login } = useNuxtApp()
        await $login({ access_token: response.access_token })
        await navigateTo('/dashboard')
      } else {
        await navigateTo('/login')
      }
    } else {
      $toast('Invalid verification code', { type: 'error' })
    }
  } catch (error) {
    $toast(getErrorMessage(error), { type: 'error' })
  } finally {
    verifying.value = false
  }
}

const manualVerify = async () => {
  verifying.value = true
  
  try {
    // For testing purposes - replace with actual verification check
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    $toast('Email verified successfully!', { type: 'success' })
    
    // Clear verification tokens
    verificationToken.value = null
    userEmail.value = ''
    
    await navigateTo('/login')
  } catch (error) {
    $toast(getErrorMessage(error), { type: 'error' })
  } finally {
    verifying.value = false
  }
}

const resendEmail = async () => {
  try {
    // Replace with actual API call
    await $apiFetch('/auth/resend-verification', {
      token: verificationToken.value
    })
    
    $toast('Verification email sent!', { type: 'success' })
    
    // Start cooldown
    resendCooldown.value = 60
    const interval = setInterval(() => {
      resendCooldown.value--
      if (resendCooldown.value <= 0) {
        clearInterval(interval)
      }
    }, 1000)
  } catch (error) {
    $toast(getErrorMessage(error), { type: 'error' })
  }
}

const switchVerificationMethod = () => {
  verificationMethod.value = verificationMethod.value === 'otp' ? 'link' : 'otp'
  // Reset OTP
  otpDigits.value = ['', '', '', '', '', '']
}
</script>

<style scoped>
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}
</style>
