import { ref, readonly } from 'vue'
import type { Profile, ProfileResponse, ProfileUpdatePayload, PasswordChangePayload } from '@/types/user.types'

export const useProfile = () => {
  const profile = ref<Profile | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const updating = ref(false)
  const changingPassword = ref(false)
  const { user } = useUser()

  const updateUserState = (data: Profile) => {
    profile.value = data
    if (user.value) {
      // Update user data with new profile information
      const updatedUser = {
        ...user.value,
        name: data.name,
        email: data.email,
        subdomain: data.subdomain,
        phoneCountryCode: data.phoneCountryCode,
        phoneNumber: data.phoneNumber,
        avatar: data.avatar
      }

      // Update the user cookie with new data
      user.value = updatedUser
    }
  }

  // Helper for API calls with error handling
  const handleApiCall = async <T>(
    apiCall: () => Promise<T>,
    loadingRef: any,
    successMessage?: string
  ): Promise<T> => {
    loadingRef.value = true
    error.value = null

    try {
      const result = await apiCall()
      if (successMessage) $toast(successMessage, { type: 'success' })
      return result
    } catch (err: any) {
      const errorMessage = err?.data?.message || 'An error occurred'
      error.value = errorMessage
      $toast(errorMessage, { type: 'error' })
      throw err
    } finally {
      loadingRef.value = false
    }
  }

  const fetchProfile = () => handleApiCall(
    async () => {
      const response = await $apiFetch<ProfileResponse>('/profile', undefined, { method: 'GET' })
      const mappedResponse: Profile = {
        ...response,
        avatar: response.profileImageUrl
      }
      updateUserState(mappedResponse)
      return mappedResponse
    },
    loading
  )

  const updateProfile = (payload: ProfileUpdatePayload) => handleApiCall(
    async () => {
      const response = await $apiFetch<ProfileResponse>('/profile', payload, { method: 'PUT' })
      const mappedResponse: Profile = {
        ...response,
        avatar: response.profileImageUrl
      }
      updateUserState(mappedResponse)
      return mappedResponse
    },
    updating,
    'Profile updated successfully'
  )

  const updateProfileWithAvatar = (payload: Omit<ProfileUpdatePayload, 'avatar'>, avatarFile: File) => handleApiCall(
    async () => {
      const formData = new FormData()
      Object.entries(payload).forEach(([key, value]) => formData.append(key, value))
      formData.append('avatar', avatarFile)

      const response = await $apiFetch<ProfileResponse>('/profile', formData, { method: 'PUT' })
      const mappedResponse: Profile = {
        ...response,
        avatar: response.profileImageUrl
      }
      updateUserState(mappedResponse)
      return mappedResponse
    },
    updating,
    'Profile updated successfully'
  )

  const changePassword = (payload: PasswordChangePayload) => handleApiCall(
    () => $apiFetch('/profile/password', payload, { method: 'PUT' }),
    changingPassword,
    'Password changed successfully'
  )

  return {
    // State
    profile: readonly(profile),
    loading: readonly(loading),
    error: readonly(error),
    updating: readonly(updating),
    changingPassword: readonly(changingPassword),
    
    // Actions
    fetchProfile,
    updateProfile,
    updateProfileWithAvatar,
    changePassword,
    refreshProfile: fetchProfile,
    clearError: () => error.value = null
  }
}