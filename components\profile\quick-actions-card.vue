<template>
  <div>
    <!-- Card -->
    <div class="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
      <h3
        class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"
      >
        <Icon name="lucide:zap" class="w-5 h-5 text-yellow-500" />
        Quick Actions
      </h3>

      <div class="space-y-3">
        <button
          v-for="action in actions"
          :key="action.key"
          @click="handleAction(action)"
          :class="[
            'w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors',
            action.danger
              ? 'text-red-600 hover:bg-red-50'
              : 'text-gray-700 hover:bg-gray-50',
          ]"
        >
          <Icon
            :name="action.icon"
            class="w-5 h-5 mr-3"
            :class="action.danger ? 'text-red-500' : 'text-gray-500'"
          />
          {{ action.label }}
        </button>
      </div>
    </div>

    <!-- Change Password Modal -->
    <ChangePasswordModal
      :show="showPasswordModal"
      @close="showPasswordModal = false"
      @success="handlePasswordSuccess"
    />

    <!-- Delete Modal -->
    <DeleteAccountModal
      :show="showDeleteModal"
      @close="showDeleteModal = false"
      @confirm="deleteAccount"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChangePasswordModal from "~/components/ui/modal/ChangePassword.vue"
import DeleteAccountModal from "~/components/ui/modal/DeleteAccount.vue"

interface Action {
  key: string
  label: string
  icon: string
  danger: boolean
}

const emit = defineEmits<{ (e: "action", action: string): void }>()

const actions: Action[] = [
  {
    key: "change-password",
    label: "Change Password",
    icon: "lucide:lock",
    danger: false,
  },
  {
    key: "upgrade",
    label: "Upgrade to Pro",
    icon: "lucide:arrow-up-right",
    danger: false,
  },
  {
    key: "delete-account",
    label: "Delete Account",
    icon: "lucide:trash-2",
    danger: true,
  },
]

const showPasswordModal = ref(false)
const showDeleteModal = ref(false)

const handleAction = (action: Action) => {
  if (action.key === "change-password") {
    showPasswordModal.value = true
  } else if (action.key === "delete-account") {
    showDeleteModal.value = true
  } else {
    emit("action", action.key)
  }
}

const handlePasswordSuccess = () => {
  // Optional: Show success message or perform additional actions
  console.log('Password changed successfully!')
}

const deleteAccount = () => {
  showDeleteModal.value = false
  emit("action", "delete-account")
}
</script>