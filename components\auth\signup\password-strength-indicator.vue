<template>
  <div class="mt-2 space-y-1">
    <div class="flex items-center gap-2">
      <div 
        class="w-2 h-2 rounded-full transition-colors" 
        :class="strength.length ? 'bg-green-500' : 'bg-red-500'"
      />
      <span 
        class="text-xs transition-colors" 
        :class="strength.length ? 'text-green-600' : 'text-red-600'"
      >
        At least 8 characters
      </span>
    </div>
    <div class="flex items-center gap-2">
      <div 
        class="w-2 h-2 rounded-full transition-colors" 
        :class="strength.hasUppercase ? 'bg-green-500' : 'bg-red-500'"
      />
      <span 
        class="text-xs transition-colors" 
        :class="strength.hasUppercase ? 'text-green-600' : 'text-red-600'"
      >
        One uppercase letter
      </span>
    </div>
    <div class="flex items-center gap-2">
      <div 
        class="w-2 h-2 rounded-full transition-colors" 
        :class="strength.hasNumber ? 'bg-green-500' : 'bg-red-500'"
      />
      <span 
        class="text-xs transition-colors" 
        :class="strength.hasNumber ? 'text-green-600' : 'text-red-600'"
      >
        One number
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  password: string
}

const props = defineProps<Props>()

const strength = computed(() => ({
  length: props.password.length >= 8,
  hasUppercase: /[A-Z]/.test(props.password),
  hasNumber: /\d/.test(props.password)
}))
</script>