<template>
  <ClientOnly fallback-tag="div" fallback="Loading form...">
    <form @submit.prevent="handleRegister" class="space-y-6">
      <InputField
        v-model="form.email"
        label="Email"
        type="email"
        placeholder="Enter your email"
        :required="true"
      >
        <template #icon>
          <Icon name="lucide:mail" class="w-5 h-5 text-neutral-400" />
        </template>
      </InputField>

      <InputField
        v-model="form.password"
        label="Password"
        :type="showPassword ? 'text' : 'password'"
        placeholder="Enter your password"
        :required="true"
        @input="validatePassword"
      >
        <template #icon>
          <button
            type="button"
            @click="showPassword = !showPassword"
            class="text-neutral-400 hover:text-primary-600 transition-colors"
          >
            <Icon
              :name="showPassword ? 'lucide:eye-off' : 'lucide:eye'"
              class="w-5 h-5"
            />
          </button>
        </template>
      </InputField>

      <!-- Password strength indicator -->
      <div v-if="form.password" class="space-y-2">
        <div class="text-sm text-neutral-600">Password strength:</div>
        <div class="space-y-1">
          <div class="flex items-center space-x-2">
            <div
              class="w-2 h-2 rounded-full"
              :class="
                passwordStrength.length ? 'bg-green-500' : 'bg-neutral-300'
              "
            ></div>
            <span
              class="text-xs"
              :class="
                passwordStrength.length ? 'text-green-600' : 'text-neutral-500'
              "
            >
              At least 8 characters
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <div
              class="w-2 h-2 rounded-full"
              :class="
                passwordStrength.hasUppercase
                  ? 'bg-green-500'
                  : 'bg-neutral-300'
              "
            ></div>
            <span
              class="text-xs"
              :class="
                passwordStrength.hasUppercase
                  ? 'text-green-600'
                  : 'text-neutral-500'
              "
            >
              Contains uppercase letter
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <div
              class="w-2 h-2 rounded-full"
              :class="
                passwordStrength.hasNumber ? 'bg-green-500' : 'bg-neutral-300'
              "
            ></div>
            <span
              class="text-xs"
              :class="
                passwordStrength.hasNumber
                  ? 'text-green-600'
                  : 'text-neutral-500'
              "
            >
              Contains number
            </span>
          </div>
        </div>
      </div>

      <!-- Subdomain Field -->
      <div class="space-y-2">
        <label class="block">
          <p class="text-foreground text-base font-medium leading-normal pb-2">
            Subdomain <span class="text-red-500">*</span>
          </p>
          <div class="relative">
            <input
              v-model="form.subdomain"
              type="text"
              placeholder="Enter your subdomain"
              class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-foreground focus:outline-0 focus:ring-2 focus:ring-primary border border-border bg-background focus:border-primary h-14 placeholder:text-muted-foreground p-[15px] text-base font-normal leading-normal pr-32 transition-all duration-300"
              :class="{
                'border-red-500 focus:ring-red-200': subdomainError,
                'border-green-500 focus:ring-green-200':
                  !subdomainError && form.subdomain && subdomainValid,
              }"
              required
            />
            <span
              class="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm font-medium"
            >
              .bookiime.com
            </span>

            <!-- Loading indicator -->
            <div
              v-if="subdomainChecking"
              class="absolute right-32 top-1/2 transform -translate-y-1/2"
            >
              <Icon
                name="lucide:loader-2"
                class="w-4 h-4 text-primary-500 animate-spin"
              />
            </div>

            <!-- Success indicator -->
            <div
              v-else-if="!subdomainError && form.subdomain && subdomainValid"
              class="absolute right-32 top-1/2 transform -translate-y-1/2"
            >
              <Icon name="lucide:check" class="w-4 h-4 text-green-500" />
            </div>
          </div>

          <!-- Error message -->
          <p v-if="subdomainError" class="text-red-500 text-sm mt-1">
            {{ subdomainError }}
          </p>

          <!-- Success message -->
          <p
            v-else-if="!subdomainError && form.subdomain && subdomainValid"
            class="text-green-600 text-sm mt-1"
          >
            ✓ {{ form.subdomain }}.bookiime.com is available
          </p>

          <!-- Helper text -->
          <p v-else-if="!form.subdomain" class="text-neutral-500 text-sm mt-1">
            Choose a unique subdomain for your booking page
          </p>
        </label>
      </div>

      <!-- Phone Field -->
      <auth-signup-phone-field
        v-model="form.phoneNumber"
        @validation-change="phoneValid = $event"
      />

      <button
        type="submit"
        :disabled="loading"
        class="w-full py-4 font-semibold rounded-xl shadow-lg transition-all duration-300 transform focus:outline-none focus:ring-4 focus:ring-primary-200"
        :class="[
          'primary-background text-white shadow-2xl shadow-primary-500/30 hover:shadow-primary-500/50',
          {
            'opacity-50 cursor-not-allowed': loading,
            'hover:shadow-xl hover:scale-[1.02]': !loading,
            'animate-pulse': loading,
          },
        ]"
      >
        <span
          v-if="!loading"
          class="flex items-center justify-center space-x-2"
        >
          <Icon name="lucide:user-plus" class="w-5 h-5" />
          <span>Create Account</span>
        </span>

        <span v-else class="flex items-center justify-center space-x-2">
          <Icon name="lucide:loader-2" class="w-5 h-5 animate-spin" />
          <span>Creating Account...</span>
        </span>
      </button>
    </form>
  </ClientOnly>
</template>

<script setup lang="ts">
import { $toast } from "@/composables/useToast";
import type { RegistrationForm } from "~/types/user.types";

const loading = ref(false);
const showPassword = ref(false);
const subdomainError = ref<string | null>(null);
const subdomainValid = ref(false);
const subdomainChecking = ref(false);
const phoneValid = ref(false);

const form = ref<RegistrationForm>({
  email: "",
  password: "",
  subdomain: "",
  phoneCountryCode: "",
  phoneNumber: "",
});

const payload = {
  email: form.value.email,
  password: form.value.password,
  subdomain: form.value.subdomain,
  phoneCountryCode: form.value.phoneCountryCode,
  phoneNumber: form.value.phoneNumber,
};

const passwordStrength = computed(() => {
  if (!form) return { length: false, hasUppercase: false, hasNumber: false };
  const password = form.value.password || "";
  return {
    length: password.length >= 8,
    hasUppercase: /[A-Z]/.test(password),
    hasNumber: /\d/.test(password),
  };
});

// const isFormValid = computed(() => {
//   if (!form) return false;
//   return (
//     form.value.email &&
//     form.value.password &&
//     form.value.subdomain &&
//     form.value.phoneCountryCode &&
//     form.value.phoneNumber &&
//     passwordStrength.value.length &&
//     passwordStrength.value.hasUppercase &&
//     passwordStrength.value.hasNumber &&
//     subdomainValid.value &&
//     phoneValid.value
//   );
// });

const validatePassword = () => {
  // Validation handled by computed property
};

// Handle phone validation from enhanced component
const handlePhoneValidation = (isValid: boolean) => {
  phoneValid.value = isValid;
};

// Debounced subdomain validation
// const validateSubdomain = useDebounceFn(async () => {
//   if (!registrationForm) return;
//   const subdomain = registrationForm.subdomain?.toLowerCase() || "";

//   // Reset states
//   subdomainError.value = null;
//   subdomainValid.value = false;
//   subdomainSuggestions.value = [];

//   if (!subdomain) {
//     return;
//   }

//   // Basic validation
//   if (subdomain.length < 3) {
//     subdomainError.value = "Subdomain must be at least 3 characters long";
//     return;
//   }

//   if (!/^[a-z0-9-]+$/.test(subdomain)) {
//     subdomainError.value =
//       "Subdomain can only contain letters, numbers, and hyphens";
//     return;
//   }

//   if (subdomain.startsWith("-") || subdomain.endsWith("-")) {
//     subdomainError.value = "Subdomain cannot start or end with a hyphen";
//     return;
//   }

//   // Check against reserved subdomains
//   const reserved = [
//     "admin",
//     "api",
//     "www",
//     "test",
//     "demo",
//     "mail",
//     "ftp",
//     "support",
//     "help",
//     "blog",
//     "docs",
//   ];
//   if (reserved.includes(subdomain)) {
//     subdomainError.value = "This subdomain is reserved";
//     return;
//   }

//   // Check availability with backend
//   subdomainChecking.value = true;
//   try {
//     const config = useRuntimeConfig();
//     const response = await $fetch<{
//       available: boolean;
//       suggestions?: string[];
//     }>(`${config.public.apiBase}/auth/check-subdomain/${subdomain}`, {
//       method: "GET",
//       headers: {
//         Accept: "application/json",
//       },
//     });

//     if (response.available) {
//       subdomainValid.value = true;
//     } else {
//       subdomainError.value = "This subdomain is already taken";
//       // Show suggestions if available
//       if (response.suggestions && response.suggestions.length > 0) {
//         subdomainSuggestions.value = response.suggestions;
//       }
//     }
//   } catch (error: any) {
//     // If backend endpoint doesn't exist (404) or other errors, allow to continue with warning
//     console.warn("Subdomain check failed:", error);

//     if (error.status === 404) {
//       // Endpoint doesn't exist yet - allow to continue but show info
//       subdomainError.value = null;
//       subdomainValid.value = true;
//       console.info(
//         "Subdomain check endpoint not implemented yet - allowing registration to continue"
//       );
//     } else {
//       // Other errors - show warning but allow to continue
//       subdomainError.value =
//         "Unable to verify subdomain availability. Registration will continue.";
//       subdomainValid.value = true;
//     }
//   } finally {
//     subdomainChecking.value = false;
//   }
// }, 500);

// Handle suggestion selection

const handleRegister = async () => {
  if (!form.value) {
    $toast("Please fill in all required fields correctly", { type: "error" });
    return;
  }

  loading.value = true;

  try {
    const payload = {
      email: form.value.email,
      password: form.value.password,
      subdomain: form.value.subdomain,
      phoneCountryCode: form.value.phoneCountryCode,
      phoneNumber: form.value.phoneNumber,
    };

    const response = await $apiFetch("/users", payload);
    $toast("🎉Welcome to Bookiime!! Account created successfully! Please sign in.", {
      type: "success",
      title: "Account Created!",
      timeout: 5000,
    });
    await navigateTo("/login");
  } catch (error) {
    $toast(getErrorMessage(error), { type: "error" });
  } finally {
    loading.value = false;
  }
};
function useDebounceFn(fn: () => Promise<void>, delay: number) {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return () => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      fn();
    }, delay);
  };
}
</script>
