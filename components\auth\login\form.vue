<template>
  <form @submit.prevent="handleLogin" class="space-y-6">
    <InputField
      v-model="email"
      label="Email"
      type="email"
      placeholder="Enter your email"
      :required="true"
    >
      <template #icon>
        <Icon name="lucide:user" class="w-5 h-5 text-neutral-400" />
      </template>
    </InputField>

    <InputField
      v-model="password"
      label="Password"
      :type="showPassword ? 'text' : 'password'"
      placeholder="Enter your password"
      :required="true"
    >
      <template #icon>
        <button
          type="button"
          @click="showPassword = !showPassword"
          class="text-neutral-400 hover:text-primary-600 transition-colors"
        >
          <Icon
            :name="showPassword ? 'lucide:eye-off' : 'lucide:eye'"
            class="w-5 h-5"
          />
        </button>
      </template>
    </InputField>

    <div class="flex items-center justify-between">
      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          v-model="rememberMe"
          type="checkbox"
          class="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
        />
        <span class="text-body-3 text-neutral-600">Remember me</span>
      </label>
      <button
        type="button"
        @click="handleForgotPassword"
        class="text-body-3 text-primary-600 hover:text-primary-700 transition-colors"
      >
        Recovery Password
      </button>
    </div>

    <button
      type="submit"
      :disabled="loading"
      class="w-full py-4 primary-background text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed text-body-2"
    >
      <span v-if="!loading">Login</span>
      <span v-else class="flex items-center justify-center space-x-2">
        <Icon name="lucide:loader-2" class="w-5 h-5 animate-spin" />
        <span>Signing in...</span>
      </span>
    </button>

    <button
      type="button"
      @click="handleGoogleSignIn"
      :disabled="loading"
      class="w-full py-4 bg-white border border-neutral-200 text-neutral-700 font-medium rounded-xl hover:bg-neutral-50 transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <Icon name="lucide:chrome" class="w-5 h-5" />
      <span>Sign in with Google</span>
    </button>

    <div class="text-center pt-4">
      <p class="text-body-3 text-neutral-600">
        Don't have an account yet?
        <button
          type="button"
          @click="handleSignUp"
          class="text-primary-600 hover:text-primary-700 font-medium transition-colors ml-1"
        >
          Sign Up
        </button>
      </p>
    </div>
  </form>
</template>

<script setup lang="ts">
const email = ref("");
const password = ref("");
const rememberMe = ref(false);
const showPassword = ref(false);
const loading = ref(false);

async function handleLogin() {
  const payload = {
    email: email.value,
    password: password.value,
  };

  loading.value = true;

  try {
    const response = await $apiFetch<{ access_token: string }>(
      "/auth/login",
      payload
    );

    $toast("Signed in successfully", { type: "success" });

    const { $login } = useNuxtApp();
    await $login(response);

    return navigateTo({ name: "dashboard" });
  } catch (error) {
    $toast(getErrorMessage(error), { type: "error" });
  } finally {
    loading.value = false;
  }
}

function handleForgotPassword() {
  // TODO: Implement forgot password functionality
  $toast("Forgot password functionality coming soon", { type: "info" });
}

function handleGoogleSignIn() {
  // TODO: Implement Google Sign-in
  $toast("Google Sign-in functionality coming soon", { type: "info" });
}

function handleSignUp() {
  navigateTo('/signup');
}
</script>
