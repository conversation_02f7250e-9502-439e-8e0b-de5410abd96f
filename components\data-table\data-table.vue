<script setup lang="ts" generic="TData, TValue">
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  TableState,
} from '@tanstack/vue-table'
import {
  FlexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useVueTable,
} from "@tanstack/vue-table"
import { ref } from 'vue'

// Props definition
const props = defineProps<{
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
}>()

// Table state with proper typing
const sorting = ref<SortingState>([])
const columnFilters = ref<ColumnFiltersState>([])
const columnVisibility = ref<Record<string, boolean>>({})

// Initialize table with proper state handling
const table = useVueTable({
  get data() { return props.data },
  get columns() { return props.columns },
  getCoreRowModel: getCoreRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  state: {
    get sorting() { return sorting.value },
    get columnFilters() { return columnFilters.value },
    get columnVisibility() { return columnVisibility.value },
  },
  onSortingChange: (updater) => {
    sorting.value = typeof updater === 'function' 
      ? updater(sorting.value) 
      : updater
  },
  onColumnFiltersChange: (updater) => {
    columnFilters.value = typeof updater === 'function'
      ? updater(columnFilters.value)
      : updater
  },
  onColumnVisibilityChange: (updater) => {
    columnVisibility.value = typeof updater === 'function'
      ? updater(columnVisibility.value)
      : updater
  },
})

// Type-safe filter value handler
const handleFilterValue = (value: string, columnId: string) => {
  table.getColumn(columnId)?.setFilterValue(value)
}
</script>

<template>
  <div>
    <!-- Search and Column Visibility -->
    <div class="flex items-center justify-between py-4">
      <Input 
        placeholder="Filter..." 
        class="max-w-sm"
        :model-value="table.getColumn('email')?.getFilterValue() as string"
        @update:model-value="(value: string) => handleFilterValue(value, 'email')"
      />
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" class="ml-auto">
            Columns
            <Icon name="lucide:chevron-down" class="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuCheckboxItem
            v-for="column in table.getAllColumns().filter(col => col.getCanHide())"
            :key="column.id"
            :model-value="column.getIsVisible()"
            @update:model-value="value => column.toggleVisibility(!!value)"
            class="capitalize"
          >
            {{ column.id }}
          </DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Table -->
    <div class="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id">
            <TableHead v-for="header in headerGroup.headers" :key="header.id">
              <div v-if="!header.isPlaceholder" class="flex items-center">
                <FlexRender
                  :render="header.column.columnDef.header"
                  :props="header.getContext()"
                />
                <Button
                  v-if="header.column.getCanSort()"
                  variant="ghost"
                  class="p-0 h-8 ml-2"
                  @click="header.column.toggleSorting()"
                >
                  <Icon 
                    name="lucide:arrow-up-down" 
                    class="w-4 h-4"
                  />
                </Button>
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <template v-if="table.getRowModel().rows?.length">
            <TableRow 
              v-for="row in table.getRowModel().rows"
              :key="row.id"
              :data-state="row.getIsSelected() ? 'selected' : undefined"
            >
              <TableCell v-for="cell in row.getVisibleCells()" :key="cell.id">
                <FlexRender
                  :render="cell.column.columnDef.cell"
                  :props="cell.getContext()"
                />
              </TableCell>
            </TableRow>
          </template>
          <template v-else>
            <TableRow>
              <TableCell 
                :colspan="columns.length" 
                class="h-24 text-center"
              >
                No results.
              </TableCell>
            </TableRow>
          </template>
        </TableBody>
      </Table>
    </div>

    <!-- Pagination -->
    <div class="flex items-center justify-end space-x-2 py-4">
      <Button
        variant="outline"
        size="sm"
        @click="table.previousPage()"
        :disabled="!table.getCanPreviousPage()"
      >
        Previous
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="table.nextPage()"
        :disabled="!table.getCanNextPage()"
      >
        Next
      </Button>
    </div>
  </div>
</template>