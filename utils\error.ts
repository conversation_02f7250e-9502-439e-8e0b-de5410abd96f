import { FetchError } from "ofetch";

type ErrorResponse = {
  message: string;
  status: boolean;
};

/**
 * Utility function to extract error messages from various error types./
 * @param error - The error object to extract the message from.
 * @returns A string containing the error message.
 * This is used in coordination with the useToast composable to display error messages.
 * Example usage: $toast(getErrorMessage(error), { type: 'error' });
 */
export const getErrorMessage = (error: unknown) => {
  if (error instanceof FetchError) {
    if (error.response) {
      const errorData = error.response._data as ErrorResponse;
      if (errorData && errorData.message) {
        return errorData.message;
      }
    }
    return `Network error: ${error.message}`; 
  }
  if (error instanceof Error) return error.message;
  if (typeof error === "string") return error;
  return "An unknown error occurred";
};
