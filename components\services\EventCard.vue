<template>
  <div class="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
    <!-- Event Image -->
    <div class="aspect-video bg-neutral-100 relative overflow-hidden">
      <img 
        v-if="event.imageUrl" 
        :src="event.imageUrl" 
        :alt="event.title"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-full h-full flex items-center justify-center">
        <Icon name="lucide:calendar" class="w-12 h-12 text-neutral-400" />
      </div>
      
      <!-- Status Badge -->
      <div class="absolute top-3 right-3">
        <span 
          :class="[
            'px-2 py-1 text-xs font-medium rounded-full',
            event.isActive 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          ]"
        >
          {{ event.isActive ? 'Active' : 'Inactive' }}
        </span>
      </div>
    </div>

    <!-- Event Content -->
    <div class="p-4">
      <!-- Title and Category -->
      <div class="mb-3">
        <h3 class="font-semibold text-neutral-900 text-lg mb-1 line-clamp-1">
          {{ event.title }}
        </h3>
        <p v-if="event.category" class="text-sm text-primary-600 font-medium">
          {{ event.category }}
        </p>
      </div>

      <!-- Description -->
      <p class="text-sm text-neutral-600 mb-4 line-clamp-2">
        {{ event.description }}
      </p>

      <!-- Event Details -->
      <div class="space-y-2 mb-4">
        <!-- Date Range -->
        <div class="flex items-center text-sm text-neutral-600">
          <Icon name="lucide:calendar" class="w-4 h-4 mr-2 flex-shrink-0" />
          <span>{{ formatDateRange(event.startDate, event.endDate) }}</span>
        </div>

        <!-- Time -->
        <div class="flex items-center text-sm text-neutral-600">
          <Icon name="lucide:clock" class="w-4 h-4 mr-2 flex-shrink-0" />
          <span>{{ event.startTime }} - {{ event.endTime }}</span>
        </div>

        <!-- Location -->
        <div v-if="event.location" class="flex items-center text-sm text-neutral-600">
          <Icon name="lucide:map-pin" class="w-4 h-4 mr-2 flex-shrink-0" />
          <span class="line-clamp-1">{{ event.location }}</span>
        </div>

        <!-- Attendees -->
        <div v-if="event.maxAttendees" class="flex items-center text-sm text-neutral-600">
          <Icon name="lucide:users" class="w-4 h-4 mr-2 flex-shrink-0" />
          <span>{{ event.currentAttendees || 0 }}/{{ event.maxAttendees }} attendees</span>
        </div>
      </div>

      <!-- Price and Actions -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <span v-if="event.price && event.price > 0" class="text-lg font-bold text-neutral-900">
            ${{ event.price.toFixed(2) }}
          </span>
          <span v-else class="text-lg font-bold text-green-600">
            Free
          </span>
        </div>

        <div class="flex items-center space-x-2">
          <button 
            @click="$emit('edit', event)"
            class="p-2 text-neutral-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Edit event"
          >
            <Icon name="lucide:edit-2" class="w-4 h-4" />
          </button>
          <button 
            @click="$emit('delete', event)"
            class="p-2 text-neutral-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            title="Delete event"
          >
            <Icon name="lucide:trash-2" class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Event } from '~/types/service.types'

interface Props {
  event: Event
}

interface Emits {
  edit: [event: Event]
  delete: [event: Event]
}

defineProps<Props>()
defineEmits<Emits>()

// Helper function to format date range
const formatDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  const options: Intl.DateTimeFormatOptions = { 
    month: 'short', 
    day: 'numeric',
    year: start.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
  }
  
  const startFormatted = start.toLocaleDateString('en-US', options)
  
  // If same day, just show one date
  if (start.toDateString() === end.toDateString()) {
    return startFormatted
  }
  
  // If different days, show range
  const endFormatted = end.toLocaleDateString('en-US', options)
  return `${startFormatted} - ${endFormatted}`
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
