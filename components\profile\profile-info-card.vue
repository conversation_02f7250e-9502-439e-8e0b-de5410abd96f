<template>
  <div class="bg-white rounded-2xl border border-neutral-200 p-6 shadow-sm">
    <h2 class="text-xl font-semibold text-neutral-900 mb-6">Personal Information</h2>

    <div v-if="loading" class="space-y-6">
      <!-- Loading skeleton -->
      <div v-for="i in 5" :key="i" class="space-y-2">
        <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
        <div class="w-full h-12 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>

    <div v-else-if="error" class="text-center py-8">
      <p class="text-red-600 text-sm mb-4">{{ error }}</p>
      <button
        @click="refreshProfile"
        class="text-blue-600 text-sm hover:text-blue-700 underline"
      >
        Try again
      </button>
    </div>

    <form v-else @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Profile Picture -->
      <div class="flex items-center space-x-6">
        <div class="relative">
          <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-white rounded-2xl flex items-center justify-center shadow-lg">
            <img
              v-if="form.avatar"
              :src="form.avatar"
              alt="Profile"
              class="w-20 h-20 rounded-2xl object-cover"
            />
            <div
              v-else-if="form.name"
              class="w-20 h-20 rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white text-2xl font-bold"
            >
              {{ form.name }}
            </div>
            <Icon v-else name="lucide:user" class="w-10 h-10 text-white" />
          </div>
          <button
            type="button"
            @click="triggerAvatarUpload"
            :disabled="updating"
            class="absolute -bottom-2 -right-2 w-8 h-8 bg-white border-2 border-neutral-200 rounded-full flex items-center justify-center hover:bg-neutral-50 transition-colors shadow-sm disabled:opacity-50"
          >
            <Icon
              :name="updating ? 'lucide:loader-2' : 'lucide:camera'"
              :class="[
                'w-4 h-4 text-neutral-600',
                { 'animate-spin': updating }
              ]"
            />
          </button>
          <input
            ref="avatarInput"
            type="file"
            accept="image/*"
            @change="handleAvatarChange"
            class="hidden"
          />
        </div>
        <div class="flex-1">
          <h3 class="font-medium text-neutral-900">Profile Picture</h3>
          <p class="text-sm text-neutral-500 mt-1">Upload a new profile picture</p>

          <!-- Upload immediately button when file is selected -->
          <button
            v-if="avatarFile"
            @click="uploadAvatarImmediately"
            :disabled="updating"
            type="button"
            class="mt-2 px-3 py-1 text-xs bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 transition-colors"
          >
            <span v-if="updating" class="flex items-center">
              <Icon name="lucide:loader-2" class="w-3 h-3 mr-1 animate-spin" />
              Uploading...
            </span>
            <span v-else>Upload Now</span>
          </button>
        </div>
      </div>

      <!-- Full Name -->
      <div>
        <label for="name" class="block text-sm font-medium text-neutral-900 mb-2">
          Full Name
        </label>
        <input
          id="name"
          v-model="form.name"
          type="text"
          required
          class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Enter your full name"
        />
      </div>

      <!-- Email -->
      <div>
        <label for="email" class="block text-sm font-medium text-neutral-900 mb-2">
          Email Address
        </label>
        <input
          id="email"
          v-model="form.email"
          type="email"
          required
          class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          placeholder="Enter your email address"
        />
      </div>

      <!-- Subdomain -->
      <div>
        <label for="subdomain" class="block text-sm font-medium text-neutral-900 mb-2">
          Subdomain
        </label>
        <div class="relative">
          <input
            id="subdomain"
            v-model="form.subdomain"
            type="text"
            required
            class="w-full px-4 py-3 pr-32 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="your-business"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <span class="text-neutral-500 text-sm">.bookiime.com</span>
          </div>
        </div>
        <p class="text-xs text-neutral-500 mt-1">This will be your booking page URL</p>
      </div>

      <!-- Phone Number -->
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div>
          <label for="phoneCountryCode" class="block text-sm font-medium text-neutral-900 mb-2">
            Country Code
          </label>
          <select
            id="phoneCountryCode"
            v-model="form.phoneCountryCode"
            required
            class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">Select</option>
            <option value="+233">+233 (GH)</option>
            <option value="+1">+1 (US/CA)</option>
            <option value="+234">+234 (NG)</option>
            <option value="+44">+44 (UK)</option>
            <option value="+49">+49 (DE)</option>
          </select>
        </div>
        <div class="sm:col-span-2">
          <label for="phoneNumber" class="block text-sm font-medium text-neutral-900 mb-2">
            Phone Number
          </label>
          <input
            id="phoneNumber"
            v-model="form.phoneNumber"
            type="tel"
            required
            class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="Enter your phone number"
          />
        </div>
      </div>

      <!-- Submit Button -->
      <div v-if="showActions" class="flex justify-end">
        <button
          type="submit"
          :disabled="updating"
          class="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="updating" class="flex items-center">
            <Icon name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
            Saving...
          </span>
          <span v-else>Save Changes</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import type { ProfileUpdatePayload } from '@/types/user.types'
import { useProfile } from '@/composables/useProfile'

interface Props {
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), { showActions: true })

// Composables
const {
  profile,
  loading,
  error,
  updating,
  fetchProfile,
  updateProfile,
  updateProfileWithAvatar,
  refreshProfile
} = useProfile()

// Form state
const form = ref<ProfileUpdatePayload>({
  name: '',
  email: '',
  subdomain: '',
  phoneCountryCode: '',
  phoneNumber: '',
  avatar: ''
})

const avatarInput = ref<HTMLInputElement | null>(null)
const avatarFile = ref<File | null>(null)

// Initialize form data when profile is loaded
const initializeForm = () => {
  if (profile.value) {
    form.value = {
      name: profile.value.name || '',
      email: profile.value.email || '',
      subdomain: profile.value.subdomain || '',
      phoneCountryCode: profile.value.phoneCountryCode || '',
      phoneNumber: profile.value.phoneNumber || '',
      avatar: profile.value.avatar || ''
    }
  }
}

// Watch for profile changes and update form
watch(profile, initializeForm, { immediate: true })

onMounted(async () => {
  await fetchProfile()
})

const handleSubmit = async () => {
  try {
    if (avatarFile.value) {
      await updateProfileWithAvatar(
        {
          name: form.value.name,
          email: form.value.email,
          subdomain: form.value.subdomain,
          phoneCountryCode: form.value.phoneCountryCode,
          phoneNumber: form.value.phoneNumber
        },
        avatarFile.value
      )
    } else {
      await updateProfile(form.value)
    }

    avatarFile.value = null
  } catch (error) {
    // Error handling is done in the composable
    console.error('Failed to update profile:', error)
  }
}

const triggerAvatarUpload = () => {
  avatarInput.value?.click()
}

// Upload avatar immediately without form submission
const uploadAvatarImmediately = async () => {
  if (!avatarFile.value) return

  try {
    await updateProfileWithAvatar(
      {
        name: form.value.name,
        email: form.value.email,
        subdomain: form.value.subdomain,
        phoneCountryCode: form.value.phoneCountryCode,
        phoneNumber: form.value.phoneNumber
      },
      avatarFile.value
    )

    // Clear the file after successful upload
    avatarFile.value = null

    // Clear the input
    if (avatarInput.value) {
      avatarInput.value.value = ''
    }

    $toast('Profile picture updated successfully!', { type: 'success' })
  } catch (error) {
    console.error('Failed to upload avatar:', error)
    $toast('Failed to upload profile picture. Please try again.', { type: 'error' })
  }
}

defineExpose({ submit: handleSubmit })

const handleAvatarChange = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  if (file.size > 5 * 1024 * 1024) {
    $toast('File size must be less than 5MB', { type: 'error' })
    return
  }

  if (!file.type.startsWith('image/')) {
    $toast('Please select a valid image file', { type: 'error' })
    return
  }

  try {
    // Store the file for upload
    avatarFile.value = file

    // Create preview URL for immediate display
    const previewUrl = URL.createObjectURL(file)
    form.value.avatar = previewUrl

    $toast('Profile picture selected. Click "Save Changes" to upload.', { type: 'info' })
  } catch (error: any) {
    const errorMessage = error?.data?.message || 'Failed to process profile picture. Please try again.'
    $toast(errorMessage, { type: 'error' })
  } finally {
    // Don't clear the input value here to allow re-selection if needed
  }
}
</script>
