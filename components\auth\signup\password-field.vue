<template>
  <div class="space-y-2">
    <label class="block">
      <p class="text-body-3 font-medium text-neutral-700 mb-2 transition-colors">
        Password
        <span class="text-primary-200 text-body-5 ml-1.5">⁕</span>
      </p>
      <div class="relative">
        <input
          :value="modelValue"
          :type="showPassword ? 'text' : 'password'"
          placeholder="Enter your password"
          class="w-full px-4 py-4 bg-neutral-50 border border-neutral-200 rounded-xl text-body-2 placeholder-neutral-400 transition-all duration-300 pr-12"
          :class="{ 'border-primary-500 bg-white focus:ring-4 focus:ring-primary-100': isFocused }"
          required
          @input="onInput"
          @focus="isFocused = true"
          @blur="isFocused = false"
        />
        <button
          type="button"
          @click="togglePasswordVisibility"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-primary-600 transition-colors"
        >
          <Icon
            :name="showPassword ? 'lucide:eye-off' : 'lucide:eye'"
            class="w-5 h-5"
          />
        </button>
      </div>
      
      <!-- Password Strength Indicators -->
      <div v-if="modelValue" class="mt-3 space-y-2">
        <div class="flex items-center gap-2">
          <div 
            class="w-2 h-2 rounded-full transition-colors" 
            :class="passwordStrength.length >= 8 ? 'bg-green-500' : 'bg-red-500'"
          ></div>
          <span 
            class="text-xs transition-colors" 
            :class="passwordStrength.length >= 8 ? 'text-green-600' : 'text-red-600'"
          >
            At least 8 characters
          </span>
        </div>
        <div class="flex items-center gap-2">
          <div 
            class="w-2 h-2 rounded-full transition-colors" 
            :class="passwordStrength.hasUppercase ? 'bg-green-500' : 'bg-red-500'"
          ></div>
          <span 
            class="text-xs transition-colors" 
            :class="passwordStrength.hasUppercase ? 'text-green-600' : 'text-red-600'"
          >
            One uppercase letter
          </span>
        </div>
        <div class="flex items-center gap-2">
          <div 
            class="w-2 h-2 rounded-full transition-colors" 
            :class="passwordStrength.hasNumber ? 'bg-green-500' : 'bg-red-500'"
          ></div>
          <span 
            class="text-xs transition-colors" 
            :class="passwordStrength.hasNumber ? 'text-green-600' : 'text-red-600'"
          >
            One number
          </span>
        </div>
        <div class="flex items-center gap-2">
          <div 
            class="w-2 h-2 rounded-full transition-colors" 
            :class="passwordStrength.hasSpecial ? 'bg-green-500' : 'bg-red-500'"
          ></div>
          <span 
            class="text-xs transition-colors" 
            :class="passwordStrength.hasSpecial ? 'text-green-600' : 'text-red-600'"
          >
            One special character
          </span>
        </div>
      </div>
    </label>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'validation-change': [isValid: boolean]
}>()

const showPassword = ref(false)
const isFocused = ref(false)

const passwordStrength = computed(() => {
  const password = props.modelValue
  return {
    length: password.length,
    hasUppercase: /[A-Z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
})

const isValid = computed(() => {
  return passwordStrength.value.length >= 8 &&
         passwordStrength.value.hasUppercase &&
         passwordStrength.value.hasNumber &&
         passwordStrength.value.hasSpecial
})

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const onInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
}

// Watch for validation changes
watch(isValid, (newValue) => {
  emit('validation-change', newValue)
}, { immediate: true })
</script>
