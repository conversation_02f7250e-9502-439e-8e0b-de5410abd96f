<template>
  <div class="space-y-2">
    <label class="block">
      <p class="text-body-3 font-medium text-neutral-700 mb-2 transition-colors">
        Phone Number
        <span class="text-primary-200 text-body-5 ml-1.5">⁕</span>
      </p>
      <div class="relative flex">
        <!-- Country Code Selector -->
        <div class="relative">
          <button
            type="button"
            @click="showCountryDropdown = !showCountryDropdown"
            class="flex items-center gap-2 px-3 py-4 bg-neutral-50 border border-neutral-200 border-r-0 rounded-l-xl text-body-2 hover:bg-neutral-100 transition-all duration-300 min-w-[100px]"
            :class="{ 'border-primary-500 bg-white': isFocused }"
          >
            <span class="text-lg">{{ selectedCountry.flag }}</span>
            <span class="text-sm font-medium">{{ selectedCountry.code }}</span>
            <Icon name="lucide:chevron-down" class="w-4 h-4 text-neutral-400" />
          </button>
          
          <!-- Country Dropdown -->
          <div
            v-if="showCountryDropdown"
            class="absolute top-full left-0 z-50 w-64 max-h-60 overflow-y-auto bg-white border border-neutral-200 rounded-xl shadow-lg mt-1"
          >
            <div class="p-2">
              <input
                v-model="countrySearch"
                type="text"
                placeholder="Search countries..."
                class="w-full px-3 py-2 border border-neutral-200 rounded-lg text-sm"
              />
            </div>
            <div class="max-h-48 overflow-y-auto">
              <button
                v-for="country in filteredCountries"
                :key="country.code"
                type="button"
                @click="selectCountry(country)"
                class="w-full flex items-center gap-3 px-3 py-2 hover:bg-neutral-50 transition-colors text-left"
              >
                <span class="text-lg">{{ country.flag }}</span>
                <div class="flex-1">
                  <div class="text-sm font-medium">{{ country.name }}</div>
                  <div class="text-xs text-neutral-500">{{ country.code }}</div>
                </div>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Phone Number Input -->
        <input
          :value="phoneNumber"
          type="tel"
          placeholder="Enter phone number"
          class="flex-1 px-4 py-4 bg-neutral-50 border border-neutral-200 rounded-r-xl text-body-2 placeholder-neutral-400 transition-all duration-300"
          :class="{ 'border-primary-500 bg-white focus:ring-4 focus:ring-primary-100': isFocused }"
          required
          @input="onPhoneInput"
          @focus="isFocused = true"
          @blur="isFocused = false"
          @keypress="onKeyPress"
        />
      </div>
      
      <!-- Validation Message -->
      <p v-if="validationMessage" class="text-red-500 text-sm mt-1">
        {{ validationMessage }}
      </p>
    </label>
  </div>
</template>

<script setup lang="ts">
interface Country {
  name: string
  code: string
  flag: string
  dialCode: string
}

const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'validation-change': [isValid: boolean]
}>()

const showCountryDropdown = ref(false)
const isFocused = ref(false)
const countrySearch = ref('')
const phoneNumber = ref('')
const validationMessage = ref('')

// Popular countries list (you can expand this)
const countries: Country[] = [
  { name: 'United States', code: '+1', flag: '🇺🇸', dialCode: '+1' },
  { name: 'United Kingdom', code: '+44', flag: '🇬🇧', dialCode: '+44' },
  { name: 'Canada', code: '+1', flag: '🇨🇦', dialCode: '+1' },
  { name: 'Australia', code: '+61', flag: '🇦🇺', dialCode: '+61' },
  { name: 'Germany', code: '+49', flag: '🇩🇪', dialCode: '+49' },
  { name: 'Ghana', code: '+233', flag: '🇬🇭', dialCode: '+233' },
  { name: 'Japan', code: '+81', flag: '🇯🇵', dialCode: '+81' },
  { name: 'France', code: '+33', flag: '🇫🇷', dialCode: '+33' },
  { name: 'Nigeria', code: '+234', flag: '🇳🇬', dialCode: '+234' },
  { name: 'South Africa', code: '+27', flag: '🇿🇦', dialCode: '+27' },
  { name: 'India', code: '+91', flag: '🇮🇳', dialCode: '+91' },
  { name: 'China', code: '+86', flag: '🇨🇳', dialCode: '+86' },
  { name: 'Japan', code: '+81', flag: '🇯🇵', dialCode: '+81' },
  { name: 'Brazil', code: '+55', flag: '🇧🇷', dialCode: '+55' },
]

const selectedCountry = ref<Country>(countries[0]) // Default to US

const filteredCountries = computed(() => {
  if (!countrySearch.value) return countries
  return countries.filter(country =>
    country.name.toLowerCase().includes(countrySearch.value.toLowerCase()) ||
    country.code.includes(countrySearch.value)
  )
})

const fullPhoneNumber = computed(() => {
  return phoneNumber.value ? `${selectedCountry.value.code}${phoneNumber.value}` : ''
})

const isValid = computed(() => {
  // Basic phone number validation (adjust regex as needed)
  const phoneRegex = /^\d{7,15}$/
  return phoneNumber.value ? phoneRegex.test(phoneNumber.value) : false
})

const selectCountry = (country: Country) => {
  selectedCountry.value = country
  showCountryDropdown.value = false
  countrySearch.value = ''
  updateModelValue()
}

const onPhoneInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  // Remove any non-digit characters
  const cleaned = target.value.replace(/\D/g, '')
  phoneNumber.value = cleaned
  target.value = cleaned
  
  validatePhone()
  updateModelValue()
}

const onKeyPress = (event: KeyboardEvent) => {
  // Only allow digits, backspace, delete, arrow keys
  const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab']
  if (!allowedKeys.includes(event.key) && !/\d/.test(event.key)) {
    event.preventDefault()
  }
}

const validatePhone = () => {
  if (!phoneNumber.value) {
    validationMessage.value = 'Phone number is required'
    return
  }

  if (phoneNumber.value.length < 7) {
    validationMessage.value = 'Phone number must be at least 7 digits'
    return
  }

  if (phoneNumber.value.length > 15) {
    validationMessage.value = 'Phone number must be less than 15 digits'
    return
  }

  validationMessage.value = ''
}

const updateModelValue = () => {
  emit('update:modelValue', fullPhoneNumber.value)
}

// Watch for validation changes
watch(isValid, (newValue) => {
  emit('validation-change', newValue)
}, { immediate: true })

// Close dropdown when clicking outside
onMounted(() => {
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    if (!target.closest('.relative')) {
      showCountryDropdown.value = false
    }
  })
})

// Initialize with existing value if provided
onMounted(() => {
  if (props.modelValue) {
    // Try to parse existing phone number
    const match = props.modelValue.match(/^(\+\d+)(.*)$/)
    if (match) {
      const [, code, number] = match
      const country = countries.find(c => c.code === code)
      if (country) {
        selectedCountry.value = country
        phoneNumber.value = number
      }
    }
  }
})
</script>
