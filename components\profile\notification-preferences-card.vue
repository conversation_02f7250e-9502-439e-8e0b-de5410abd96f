<template>
  <div class="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
      <Icon name="lucide:bell" class="w-5 h-5 text-primary-500" />
      Notification Preferences
    </h3>
    
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div v-for="(_ , key) in form" :key="key" class="flex items-center justify-between">
        <div>
          <label :for="key" class="text-sm font-medium text-gray-700 capitalize">
            {{ key.replace(/([A-Z])/g, ' $1').trim() }} Notifications
          </label>
          <p class="text-xs text-gray-500 mt-1">
            {{ getPreferenceDescription(key) }}
          </p>
        </div>

        <label class="relative inline-flex items-center cursor-pointer">
          <input
            :id="key"
            v-model="form[key]"
            type="checkbox"
            class="sr-only peer"
          />
          <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
        </label>
      </div>

      <div v-if="showActions" class="flex justify-end pt-4">
        <button
          type="submit"
          :disabled="loading"
          class="px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
        >
          <Icon
            v-if="loading"
            name="lucide:loader-2"
            class="w-4 h-4 animate-spin"
          />
          {{ loading ? 'Saving...' : 'Save Changes' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const { showActions } = withDefaults(defineProps<{ showActions?: boolean }>(), { showActions: true })

const form = reactive({
  email: false,
  sms: false,
  push: false,
})

const loading = ref(false)

// // Fetch current notification preferences on component mount
// onMounted(async () => {
//   await fetchNotificationPreferences()
// })

// const fetchNotificationPreferences = async () => {
//   try {
//     const profile = await $apiFetch('/profile', {
//       method: 'GET'
//     })

//     // Assuming we have notification preferences
//     if (profile.notificationPreferences) {
//       Object.assign(form, profile.notificationPreferences)
//     }
//   } catch (error) {
//     console.error('Failed to fetch notification preferences:', error)
//     $toast('Failed to load notification preferences', { type: 'error' })
//   }
// }

const handleSubmit = async () => {
  loading.value = true

  try {
    // Update notification preferences 
    await $apiFetch('/profile', {
      notificationPreferences: { ...form }
    }, { method: 'PUT' })

    // $toast('Notification preferences updated successfully!', { type: 'success' })

  } catch (error: any) {
    const errorMessage = error?.data?.message || 'Failed to update notification preferences. Please try again.'
    $toast(errorMessage, { type: 'error' })
  } finally {
    loading.value = false
  }
}

defineExpose({ submit: handleSubmit })

const getPreferenceDescription = (key: string) => {
  const descriptions: Record<string, string> = {
    email: 'Receive notifications via email',
    sms: 'Receive notifications via SMS',
    push: 'Receive push notifications'
  }

  return descriptions[key] || ''
}
</script>