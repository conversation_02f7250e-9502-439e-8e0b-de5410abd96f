<template>
  <div class="min-h-screen bg-neutral-50 overflow-x-hidden">
    <!-- Mobile Backdrop -->
    <div
      v-if="showMobileSidebar"
      class="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 lg:hidden"
      @click="showMobileSidebar = false"
    />

    <!-- Desktop Backdrop for Expanded Sidebar -->
    <div
      v-if="!sidebarCollapsed && !isMobile"
      class="fixed inset-0 bg-black/10 backdrop-blur-sm z-40 hidden lg:block"
      @click="sidebarCollapsed = true"
    />

    <!-- Sidebar -->
    <DashboardSidebar
      :show="showMobileSidebar"
      :collapsed="sidebarCollapsed"
      :navigation-items="navigationItems"
      :user="user || null"
      @close="showMobileSidebar = false"
      @logout="handleLogout"
      @toggle-collapse="sidebarCollapsed = true"
    />

    <!-- Main Content Area -->
    <div class="flex flex-col min-h-screen transition-all duration-300" :class="sidebarCollapsed ? 'lg:ml-20' : 'lg:ml-72'">
      <!-- Top Header -->
      <DashboardHeader
        :user="user || null"
        :sidebar-collapsed="sidebarCollapsed"
        @toggle-sidebar="toggleSidebar"
        @toggle-mobile-sidebar="showMobileSidebar = !showMobileSidebar"
        @logout="handleLogout"
      />

      <!-- Page Content -->
      <main class="flex-1 min-h-0 bg-gradient-to-br from-neutral-50 to-neutral-100">
        <div class="p-4 sm:p-6 lg:p-8 h-full">
          <div class="max-w-none w-full h-full">
            <slot />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
// Composables
const { user } = useUser()
const { fetchProfile } = useProfile()
const { $logout } = useNuxtApp()
const route = useRoute()

// Reactive state
const sidebarCollapsed = ref(true) // Default to collapsed
const showMobileSidebar = ref(false)
const isMobile = ref(false)

const navigationItems = computed(() => [
  {
    icon: 'lucide:layout-dashboard',
    label: 'Dashboard',
    route: '/dashboard',
    active: route.path === '/dashboard'
  },
  {
    icon: 'lucide:briefcase',
    label: 'Services',
    route: '/dashboard/services',
    active: route.path.startsWith('/dashboard/services')
  },
  {
    icon: 'lucide:calendar',
    label: 'Bookings',
    route: '/dashboard/bookings',
    active: route.path.startsWith('/dashboard/bookings')
  },
  {
    icon: 'lucide:users',
    label: 'Customers',
    route: '/dashboard/customers',
    active: route.path.startsWith('/dashboard/customers')
  },
  {
    icon: 'lucide:bar-chart-3',
    label: 'Analytics',
    route: '/dashboard/analytics',
    active: route.path.startsWith('/dashboard/analytics')
  }
])

// Computed properties - removed mainContentClasses as we're using overlay positioning

// Methods
const toggleSidebar = () => {
  if (isMobile.value) {
    showMobileSidebar.value = !showMobileSidebar.value
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

const handleLogout = async () => {
  await $logout()
}

// Responsive handling
onMounted(async () => {
  // Fetch fresh profile data to ensure user avatar is up to date
  if (user.value) {
    try {
      await fetchProfile()
    } catch (error) {
      console.warn('Failed to fetch profile data:', error)
    }
  }

  const handleResize = () => {
    const wasMobile = isMobile.value
    isMobile.value = window.innerWidth < 1024

    if (isMobile.value) {
      showMobileSidebar.value = false
      sidebarCollapsed.value = true
    } else {
      showMobileSidebar.value = false
      // Keep sidebar collapsed by default on desktop too
      if (wasMobile) {
        sidebarCollapsed.value = true
      }
    }
  }

  handleResize()
  window.addEventListener('resize', handleResize)

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
})
</script>
