<template>
  <NuxtLink
    :to="item.route"
    :class="navigationItemClasses"
  >
    <Icon
      :name="item.icon"
      class="text-white w-6 h-6"
    />
    <p class="text-white text-sm font-medium leading-normal">
      {{ item.label }}
    </p>
  </NuxtLink>
</template>

<script setup lang="ts">
interface NavigationItem {
  icon: string
  label: string
  route: string
  active?: boolean
}

interface Props {
  item: NavigationItem
}

const props = defineProps<Props>()
const route = useRoute()

// Computed properties following Open/Closed Principle
const isActive = computed(() => {
  return route.path === props.item.route || props.item.active
})

const navigationItemClasses = computed(() => {
  const baseClasses = "flex items-center gap-3 px-3 py-2 transition-colors duration-200"
  const activeClasses = "rounded-full bg-[#2c3135]"
  const inactiveClasses = "hover:bg-[#2c3135] hover:rounded-full"
  
  return `${baseClasses} ${isActive.value ? activeClasses : inactiveClasses}`
})


</script>
