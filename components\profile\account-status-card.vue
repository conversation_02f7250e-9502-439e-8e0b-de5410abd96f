<template>
  <div class="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
      <Icon name="lucide:user-check" class="w-5 h-5 text-primary-500" />
      Account Status
    </h3>
    
    <div v-if="loading" class="space-y-4">
      <!-- Loading skeleton -->
      <div v-for="i in 4" :key="i" class="flex items-center justify-between">
        <div class="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
        <div class="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
    
    <div v-else-if="error" class="text-center py-4">
      <p class="text-red-600 text-sm mb-2">Failed to load account status</p>
      <button
        @click="refreshProfile"
        class="text-blue-600 text-sm hover:text-blue-700 underline"
      >
        Try again
      </button>
    </div>
    
    <div v-else-if="profile" class="space-y-4">
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">Account Type</span>
        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full capitalize">
          {{ profile.accountType || 'Standard' }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">Member Since</span>
        <span class="text-sm text-gray-900">
          {{ formatDate(profile.memberSince) }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">Last Login</span>
        <span class="text-sm text-gray-900">
          {{ formatDate(profile.lastLogin) }}
        </span>
      </div>
      
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">Account Status</span>
        <div class="flex items-center gap-2">
          <div 
            :class="[
              'w-2 h-2 rounded-full',
              getStatusColor(profile.status)
            ]"
          ></div>
          <span 
            :class="[
              'text-sm font-medium capitalize',
              getStatusTextColor(profile.status)
            ]"
          >
            {{ profile.status || 'Active' }}
          </span>
        </div>
      </div>
      
      <!-- Additional status info if available -->
      <div v-if="profile.emailVerified !== undefined" class="flex items-center justify-between">
        <span class="text-sm text-gray-600">Email Verified</span>
        <div class="flex items-center gap-2">
          <Icon 
            :name="profile.emailVerified ? 'lucide:check-circle' : 'lucide:alert-circle'" 
            :class="[
              'w-4 h-4',
              profile.emailVerified ? 'text-green-500' : 'text-yellow-500'
            ]"
          />
          <span 
            :class="[
              'text-sm font-medium',
              profile.emailVerified ? 'text-green-600' : 'text-yellow-600'
            ]"
          >
            {{ profile.emailVerified ? 'Verified' : 'Unverified' }}
          </span>
        </div>
      </div>
      
      <div v-if="profile.twoFactorEnabled !== undefined" class="flex items-center justify-between">
        <span class="text-sm text-gray-600">Two-Factor Auth</span>
        <div class="flex items-center gap-2">
          <Icon 
            :name="profile.twoFactorEnabled ? 'lucide:shield-check' : 'lucide:shield-off'" 
            :class="[
              'w-4 h-4',
              profile.twoFactorEnabled ? 'text-green-500' : 'text-gray-400'
            ]"
          />
          <span 
            :class="[
              'text-sm font-medium',
              profile.twoFactorEnabled ? 'text-green-600' : 'text-gray-600'
            ]"
          >
            {{ profile.twoFactorEnabled ? 'Enabled' : 'Disabled' }}
          </span>
        </div>
      </div>
    </div>
    
    <div v-else class="text-center py-4">
      <p class="text-gray-500 text-sm">No profile data available</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

// Use centralized profile composable
const {
  profile,
  loading,
  error,
  fetchProfile,
  refreshProfile
} = useProfile()

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return 'N/A'

  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  } catch {
    return 'Invalid Date'
  }
}

const getStatusColor = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'bg-green-400'
    case 'inactive':
      return 'bg-gray-400'
    case 'suspended':
      return 'bg-red-400'
    default:
      return 'bg-green-400' // default to active
  }
}

const getStatusTextColor = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'text-green-600'
    case 'inactive':
      return 'text-gray-600'
    case 'suspended':
      return 'text-red-600'
    default:
      return 'text-green-600' // default to active
  }
}

// Fetch profile data on component mount if not already loaded
onMounted(async () => {
  if (!profile.value) {
    await fetchProfile()
  }
})

// Expose refresh function for parent components
defineExpose({
  refresh: refreshProfile
})
</script>