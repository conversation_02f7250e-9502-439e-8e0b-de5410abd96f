/**
 * Service utility functions for formatting and calculations
 */

/**
 * Format duration in minutes to human readable format
 * @param minutes - Duration in minutes
 * @returns Formatted duration string
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}min`;
}

/**
 * Format currency amount
 * @param amount - Amount in the smallest currency unit (e.g., cents)
 * @param currency - Currency code (default: GHS)
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = 'GHS'): string {
  // Assuming amount is in the smallest unit (e.g., pesewas for GHS)
  const value = amount / 100;
  
  if (currency === 'GHS') {
    return `₵${value.toFixed(2)}`;
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(value);
}

/**
 * Format date to human readable format
 * @param dateString - ISO date string
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Format time to human readable format
 * @param timeString - Time string in HH:mm format
 * @returns Formatted time string
 */
export function formatTime(timeString: string): string {
  const [hours, minutes] = timeString.split(':').map(Number);
  const date = new Date();
  date.setHours(hours, minutes);
  
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

/**
 * Calculate end time based on start time and duration
 * @param startTime - Start time in HH:mm format
 * @param durationMinutes - Duration in minutes
 * @returns End time in HH:mm format
 */
export function calculateEndTime(startTime: string, durationMinutes: number): string {
  const [hours, minutes] = startTime.split(':').map(Number);
  const startDate = new Date();
  startDate.setHours(hours, minutes);
  
  const endDate = new Date(startDate.getTime() + durationMinutes * 60000);
  
  return `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`;
}

/**
 * Check if a service is available for booking
 * @param service - Service object
 * @returns Boolean indicating availability
 */
export function isServiceAvailable(service: any): boolean {
  return service.isActive;
}

/**
 * Generate a slug from a string
 * @param text - Text to convert to slug
 * @returns URL-friendly slug
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Validate service form data
 * @param data - Service form data
 * @returns Validation result
 */
export function validateServiceData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!data.name || data.name.trim().length < 2) {
    errors.push('Service name must be at least 2 characters long');
  }
  
  if (!data.description || data.description.trim().length < 10) {
    errors.push('Service description must be at least 10 characters long');
  }
  
  if (!data.price || data.price <= 0) {
    errors.push('Service price must be greater than 0');
  }
  
  if (!data.duration || data.duration <= 0) {
    errors.push('Service duration must be greater than 0');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate event form data
 * @param data - Event form data
 * @returns Validation result
 */
export function validateEventData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!data.title || data.title.trim().length < 2) {
    errors.push('Event title must be at least 2 characters long');
  }
  
  if (!data.description || data.description.trim().length < 10) {
    errors.push('Event description must be at least 10 characters long');
  }
  
  if (!data.startDate) {
    errors.push('Start date is required');
  }
  
  if (!data.endDate) {
    errors.push('End date is required');
  }
  
  if (!data.startTime) {
    errors.push('Start time is required');
  }
  
  if (!data.endTime) {
    errors.push('End time is required');
  }
  
  if (data.startDate && data.endDate && new Date(data.startDate) > new Date(data.endDate)) {
    errors.push('End date must be after start date');
  }
  
  if (data.maxAttendees && data.maxAttendees <= 0) {
    errors.push('Maximum attendees must be greater than 0');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
