<script setup lang="ts">
import { ref, watch } from 'vue'
import { cn } from '@/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon } from 'lucide-vue-next'
import { DateFormatter, getLocalTimeZone } from '@internationalized/date'

const df = new DateFormatter('en-US', { dateStyle: 'long' })

// loosen type to avoid TS errors
const props = defineProps<{
  modelValue?: any
  placeholder?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', v?: any): void
}>()

const internalValue = ref<any>(props.modelValue)

watch(() => props.modelValue, (v) => {
  internalValue.value = v
})

watch(internalValue, (v) => {
  emit('update:modelValue', v)
})
</script>

<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="cn('w-[280px] justify-start text-left font-normal', !internalValue && 'text-muted-foreground')"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ internalValue ? (internalValue as any).toDate(getLocalTimeZone()) && df.format((internalValue as any).toDate(getLocalTimeZone())) : (props.placeholder || 'Pick a date') }}
      </Button>
    </PopoverTrigger>

    <PopoverContent class="w-auto p-0">
      <Calendar v-model="internalValue" initial-focus />
    </PopoverContent>
  </Popover>
</template>
