// Add the other types from the Swagger here
export type User = {
  id: string;
  name: string;
  email: string;
  access_token: string;
  subdomain?: string;
  phoneCountryCode?: string;
  phoneNumber?: string;
  avatar?: string;
};

export type Profile = {
  name: string;
  email: string;
  subdomain: string;
  phoneCountryCode: string;
  phoneNumber: string;
  avatar?: string;
  // Additional fields that can be added in the backend
  accountType?: string;
  memberSince?: string;
  lastLogin?: string;
  status?: 'active' | 'inactive' | 'suspended';
  emailVerified?: boolean;
  twoFactorEnabled?: boolean;
};

export type ProfileResponse = {
  id: string;
  name: string;
  email: string;
  subdomain: string;
  phoneCountryCode: string;
  phoneNumber: string;
  profileImageUrl?: string;
  // Additional fields that can be added in the backend
  accountType?: string;
  memberSince?: string;
  lastLogin?: string;
  status?: 'active' | 'inactive' | 'suspended';
  emailVerified?: boolean;
  twoFactorEnabled?: boolean;
};

export type ProfileUpdatePayload = {
  name: string;
  email: string;
  subdomain: string;
  phoneCountryCode: string;
  phoneNumber: string;
  avatar?: string;
};

export type PasswordChangePayload = {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
};

export type phoneNumber = {
  phoneCountryCode: string;
  phoneNumber: string;
  fullNumber: string;
};

export type RegistrationForm = {
  email: string
  password: string
  subdomain: string
  phoneCountryCode: string
  phoneNumber: string
}


