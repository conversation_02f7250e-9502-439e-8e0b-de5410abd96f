<template>
  <Dialog :open="true" @update:open="(open) => !open && $emit('close')">
    <DialogContent class="max-w-2xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>
          {{ service ? 'Edit Service' : 'Add New Service' }}
        </DialogTitle>
        <DialogDescription>
          {{ service ? 'Update your service details below.' : 'Fill in the details to create a new service.' }}
        </DialogDescription>
      </DialogHeader>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Image Upload -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">Service Image</label>
            <ImageUpload
              v-model="form.imageUrl"
              @upload="handleImageUpload"
              :loading="imageUploading"
              alt-text="Service image"
              upload-text="Upload service image"
              upload-subtext="Add an image to showcase your service"
            />
          </div>

          <!-- Service Name -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">
              Service Name <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter service name"
            />
          </div>

          <!-- Description -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">
              Description <span class="text-red-500">*</span>
            </label>
            <textarea
              v-model="form.description"
              required
              rows="3"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Describe your service"
            ></textarea>
          </div>

          <!-- Price and Duration -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                Price (₵) <span class="text-red-500">*</span>
              </label>
              <input
                v-model.number="form.price"
                type="number"
                min="0"
                step="0.01"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                Duration (minutes) <span class="text-red-500">*</span>
              </label>
              <input
                v-model.number="form.duration"
                type="number"
                min="1"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="30"
              />
            </div>
          </div>

          <!-- Category -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">Category</label>
            <select
              v-model="form.categoryId"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              :disabled="loadingCategories"
            >
              <option value="">Select a category</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
            <p v-if="loadingCategories" class="text-sm text-neutral-500">Loading categories...</p>
          </div>

          <!-- Service Overlap Toggle -->
          <div class="space-y-3 p-4 bg-neutral-50 rounded-lg border border-neutral-200">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <label class="block text-sm font-medium text-neutral-700 mb-1">
                  Service Overlap
                </label>
                <p class="text-xs text-neutral-600">
                  Enable multiple clients bookings at the same time
                </p>
              </div>
              <div class="flex items-center space-x-2">
                <Switch
                  v-model:checked="form.allowOverlap"
                  id="allowOverlap"
                />
                <label for="allowOverlap" class="text-sm text-neutral-700">
                  {{ form.allowOverlap ? 'Enabled' : 'Disabled' }}
                </label>
              </div>
            </div>
          </div>

          <!-- Active Status -->
          <div class="flex items-center space-x-2">
            <Switch
              v-model:checked="form.isActive"
              id="isActive"
            />
            <label for="isActive" class="text-sm font-medium text-neutral-700">
              Service is active and bookable
            </label>
          </div>

        <!-- Form Actions -->
        <DialogFooter>
          <Button
            type="button"
            @click="$emit('close')"
            variant="outline"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="bg-primary-600 text-white hover:bg-primary-700"
          >
            <Icon v-if="loading" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
            {{ service ? 'Update Service' : 'Create Service' }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import type { Service, ServiceFormData, ServiceCategory } from '@/types/service.types'
import { Switch } from '@/components/ui/switch'
import ImageUpload from '@/components/ui/ImageUpload.vue'
import { $apiFetch } from '@/composables/apiFetch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

interface Props {
  service?: Service | null
}

interface Emits {
  (e: 'close'): void
  (e: 'save', data: ServiceFormData, imageFile?: File | null): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form state
const loading = ref(false)
const imageUploading = ref(false)
const selectedImageFile = ref<File | null>(null)

const form = reactive<ServiceFormData>({
  name: '',
  description: '',
  price: 0,
  duration: 30,
  imageUrl: '',
  categoryId: '',
  isActive: false,
  allowOverlap: false
})

// Categories state
const categories = ref<ServiceCategory[]>([])
const loadingCategories = ref(false)

// Load categories on component mount
const loadCategories = async () => {
  loadingCategories.value = true
  try {
    categories.value = await $apiFetch<ServiceCategory[]>('/categories', undefined, { method: 'GET' })
  } catch (error) {
    console.error('Failed to load categories:', error)
  } finally {
    loadingCategories.value = false
  }
}

// Initialize form with service data if editing
watchEffect(() => {
  if (props.service) {
    Object.assign(form, {
      name: props.service.name,
      description: props.service.description,
      price: props.service.price,
      duration: props.service.duration,
      imageUrl: props.service.imageUrl || '',
      categoryId: props.service.categoryId || '',
      isActive: props.service.isActive ?? false,
      allowOverlap: props.service.allowOverlap ?? false
    })
  }
})

// Load categories when component mounts
onMounted(() => {
  loadCategories()
})

// Handle image upload
const handleImageUpload = async (file: File) => {
  selectedImageFile.value = file
  // Store the file for later use when saving the service
  // The actual upload will happen when the form is submitted
}

// Handle form submission
const handleSubmit = async () => {
  loading.value = true
  try {
    // Pass both form data and image file to parent
    emit('save', { ...form }, selectedImageFile.value)
  } finally {
    loading.value = false
  }
}
</script>
