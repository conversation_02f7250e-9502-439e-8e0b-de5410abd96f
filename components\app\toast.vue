<template>
  <Transition
    enter-from-class="opacity-0 translate-y-[-60px] scale-95 rotate-2"
    enter-active-class="transition-all ease-out duration-500"
    enter-to-class="opacity-100 translate-y-0 scale-100 rotate-0"
    leave-from-class="opacity-100 translate-y-0 scale-100 rotate-0"
    leave-active-class="transition-all ease-in duration-300"
    leave-to-class="opacity-0 translate-y-[-80px] scale-95 rotate-1"
  >
    <article
      v-if="toastState"
      class="fixed top-4 right-4 left-4 sm:left-auto sm:right-6 z-[9999] w-auto max-w-xl sm:max-w-sm"
      role="alert"
      aria-live="assertive"
    >
      <div 
        class="absolute inset-0 rounded-2xl blur-xl opacity-30 animate-pulse"
        :class="variant.glowColor"
      ></div>
      
      <div
        class="relative backdrop-blur-xl border rounded-xl overflow-hidden shadow-2xl transform hover:scale-[1.02] transition-all duration-300"
        :class="[
          variant.backgroundColor,
          variant.borderColor,
          'bg-gradient-to-br from-neutral-100/10 to-transparent'
        ]"
        @mouseenter="pauseTimeout"
        @mouseleave="resumeTimeout"
      >
        <div class="absolute inset-0 opacity-5">
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-neutral-100/20 to-transparent animate-shimmer"></div>
        </div>
        
        <div 
          v-if="toastState.options.timeout"
          class="absolute top-0 left-0 h-0.5 transition-all duration-100 ease-linear rounded-t-xl"
          :class="variant.progressColor"
          :style="{ width: `${progressWidth}%` }"
        ></div>
        
        <div class="relative p-4 flex items-start gap-3">
          <div class="shrink-0 relative">
            <div 
              class="size-9 rounded-full flex items-center justify-center relative overflow-hidden"
              :class="variant.iconBackground"
            >
              <div 
                v-if="toastState.options.type === 'error'"
                class="absolute inset-0 border border-red-200 rounded-full animate-spin"
                style="animation-duration: 3s;"
              ></div>
              
              <div 
                v-if="toastState.options.type === 'success'"
                class="absolute inset-0 border border-primary-300 rounded-full animate-pulse"
              ></div>
              
              <Icon
                :name="variant.icon"
                class="text-base relative z-10 animate-bounce"
                :class="variant.iconColor"
                style="animation-duration: 2s; animation-iteration-count: 2;"
              />
            </div>
            
            <div v-if="toastState.options.type === 'success'" class="absolute -top-0.5 -right-0.5">
              <div class="size-1.5 bg-primary-400 rounded-full animate-ping"></div>
            </div>
          </div>
          
          <div class="flex-1 min-w-0">
            <h4 
              class="font-bold text-body-3 mb-1 leading-tight"
              :class="variant.titleColor"
            >
              {{ toastState.options.title || variant.title }}
            </h4>
            <p 
              class="text-body-4 leading-relaxed opacity-90"
              :class="variant.textColor"
            >
              {{ toastState.message }}
            </p>
            
            <div v-if="toastState.options.type === 'error' && toastState.options.actions" class="mt-2 flex gap-2">
              <button
                v-for="action in toastState.options.actions"
                :key="action.label"
                class="px-2 py-1 text-body-5 font-medium rounded transition-all duration-200 hover:scale-105"
                :class="action.primary ? 'bg-red-200 text-neutral-100 hover:bg-red-100' : 'bg-neutral-100/20 text-red-200 hover:bg-neutral-100/30'"
                @click="action.handler"
              >
                {{ action.label }}
              </button>
            </div>
          </div>
          
          <button
            class="shrink-0 size-6 flex items-center justify-center rounded-full transition-all duration-200 hover:scale-110 hover:rotate-90 group"
            :class="variant.closeButtonBg"
            @click="dismiss"
            aria-label="Dismiss message"
          >
            <Icon 
              name="mdi:close" 
              class="size-3 transition-all duration-200 group-hover:scale-110" 
              :class="variant.closeButtonColor"
            />
          </button>
        </div>
        
        <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-neutral-100/30 to-transparent"></div>
      </div>
    </article>
  </Transition>
</template>

<script setup lang="ts">
import type { ToastOptions, MessageType } from '~/types/toast.types';

const { toastState, dismiss } = useToast() as {
  toastState: Ref<{
    message: string
    options: ToastOptions
  } | null>
  dismiss: () => void
}

let localTimeoutId: ReturnType<typeof setTimeout> | null = null;
let progressInterval: ReturnType<typeof setInterval> | null = null;
const progressWidth = ref(100);

const variantConfig: Record<MessageType, any> = {
  error: {
    backgroundColor: "bg-red-100/90",
    borderColor: "border-red-200/30",
    glowColor: "bg-red-200",
    titleColor: "text-neutral-100",
    textColor: "text-neutral-100",
    icon: "mdi:alert-circle-outline",
    iconBackground: "bg-red-200/20 border border-red-200/30",
    iconColor: "text-neutral-100",
    progressColor: "bg-red-200",
    closeButtonBg: "hover:bg-red-200/20",
    closeButtonColor: "text-red-200/70 hover:text-red-200",
    title: "Error Occurred",
  },
  warning: {
    backgroundColor: "bg-yellow-100/90",
    borderColor: "border-yellow-200/30",
    glowColor: "bg-yellow-200",
    titleColor: "text-yellow-200",
    textColor: "text-yellow-200/80",
    icon: "mdi:alert-outline",
    iconBackground: "bg-yellow-200/20 border border-yellow-200/30",
    iconColor: "text-yellow-200",
    progressColor: "bg-yellow-200",
    closeButtonBg: "hover:bg-yellow-200/20",
    closeButtonColor: "text-yellow-200/70 hover:text-yellow-200",
    title: "Warning",
  },
  info: {
    backgroundColor: "bg-secondary-100/90",
    borderColor: "border-secondary-200/30",
    glowColor: "bg-secondary-200",
    titleColor: "text-secondary-600",
    textColor: "text-secondary-600/80",
    icon: "mdi:information-outline",
    iconBackground: "bg-secondary-200/20 border border-secondary-200/30",
    iconColor: "text-secondary-600",
    progressColor: "bg-secondary-400",
    closeButtonBg: "hover:bg-secondary-200/20",
    closeButtonColor: "text-secondary-600/70 hover:text-secondary-600",
    title: "Information",
  },
  success: {
    backgroundColor: "bg-primary-100/90",
    borderColor: "border-primary-300/30",
    glowColor: "bg-primary-300",
    titleColor: "text-primary-600",
    textColor: "text-primary-600/80",
    icon: "mdi:check-circle-outline",
    iconBackground: "bg-primary-300/20 border border-primary-300/30",
    iconColor: "text-primary-600",
    progressColor: "bg-primary-400",
    closeButtonBg: "hover:bg-primary-200/20",
    closeButtonColor: "text-primary-600/70 hover:text-primary-600",
    title: "Success!",
  },
};

const variant = computed(() =>
  variantConfig[toastState.value?.options.type || 'info']
);

const startProgressBar = () => {
  if (toastState.value?.options.timeout) {
    const duration = toastState.value.options.timeout;
    const interval = 50; 
    const steps = duration / interval;
    let currentStep = 0;
    
    progressInterval = setInterval(() => {
      currentStep++;
      progressWidth.value = Math.max(0, 100 - (currentStep / steps) * 100);
      
      if (currentStep >= steps) {
        clearInterval(progressInterval!);
        progressInterval = null;
      }
    }, interval);
  }
};

const pauseTimeout = () => {
  if (localTimeoutId) {
    clearTimeout(localTimeoutId);
    localTimeoutId = null;
  }
  if (progressInterval) {
    clearInterval(progressInterval);
    progressInterval = null;
  }
};

const resumeTimeout = () => {
  if (toastState.value && toastState.value.options.timeout) {
    pauseTimeout();
    
    const remainingTime = (progressWidth.value / 100) * toastState.value.options.timeout;
    
    localTimeoutId = setTimeout(dismiss, remainingTime);
    
    if (remainingTime > 0) {
      const interval = 50;
      const steps = remainingTime / interval;
      let currentStep = 0;
      
      progressInterval = setInterval(() => {
        currentStep++;
        progressWidth.value = Math.max(0, progressWidth.value - (100 / steps));
        
        if (currentStep >= steps || progressWidth.value <= 0) {
          clearInterval(progressInterval!);
          progressInterval = null;
        }
      }, interval);
    }
  }
};

onMounted(() => {
  if (toastState.value && toastState.value.options.timeout) {
    localTimeoutId = setTimeout(dismiss, toastState.value.options.timeout);
    startProgressBar();
  }
});

onUnmounted(() => {
  if (localTimeoutId) {
    clearTimeout(localTimeoutId);
  }
  if (progressInterval) {
    clearInterval(progressInterval);
  }
});
</script>

<style scoped>
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

.animate-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}
</style>