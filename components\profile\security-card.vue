<template>
  <div class="bg-white rounded-xl border border-gray-200 p-6 shadow-sm" data-security-card>
    <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2">
      <Icon name="lucide:shield" class="w-5 h-5 text-red-500" />
      Security Settings
    </h2>
    
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <FormField 
        label="Current Password"
        type="password"
        required
        v-model="form.currentPassword"
        placeholder="Enter your current password"
      />

      <FormField 
        label="New Password"
        type="password"
        required
        v-model="form.newPassword"
        placeholder="Enter your new password"
      />

      <FormField 
        label="Confirm New Password"
        type="password"
        required
        v-model="form.confirmPassword"
        placeholder="Confirm your new password"
      />

      <div class="flex justify-end pt-4">
        <button
          type="submit"
          :disabled="changingPassword || !isFormValid"
          class="px-6 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2"
        >
          <Icon
            v-if="changingPassword"
            name="lucide:loader-2"
            class="w-4 h-4 animate-spin"
          />
          {{ changingPassword ? 'Changing...' : 'Change Password' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
import type { PasswordChangePayload } from '@/types/user.types'

// Use centralized profile composable
const { changingPassword, changePassword } = useProfile()

const form = reactive<PasswordChangePayload>({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const isFormValid = computed(() => {
  return form.currentPassword.length > 0 &&
         form.newPassword.length >= 8 &&
         form.newPassword === form.confirmPassword
})

const handleSubmit = async () => {
  if (!isFormValid.value) return

  try {
    await changePassword(form)

    // Reset form after successful submission
    form.currentPassword = ''
    form.newPassword = ''
    form.confirmPassword = ''
  } catch (error) {
    // Error handling is done in the composable
    console.error('Failed to change password:', error)
  }
}
</script>