<template>
  <div
    v-if="show"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4"
  >
    <div class="bg-white rounded-xl p-6 max-w-md w-full shadow-lg">
      <h2 class="text-xl font-semibold text-red-600 mb-3">
        Delete Account
      </h2>
      <p class="text-gray-600 mb-6">
        Are you sure you want to delete your account? This action is
        <strong>permanent</strong> and cannot be undone. All your data will be lost.
      </p>

      <div class="flex justify-end gap-3">
        <button
          @click="close"
          class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          @click="confirmDelete"
          class="px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700"
        >
          Delete
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ show: boolean }>()
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'confirm'): void
}>()

const close = () => emit('close')
const confirmDelete = () => emit('confirm')
</script>
