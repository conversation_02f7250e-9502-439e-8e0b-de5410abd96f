<template>
  <SwitchThumb
    v-bind="reactiveOmit(props, 'class')"
    :class="cn(
      'pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0',
      props.class
    )"
  />
</template>

<script setup lang="ts">
import type { SwitchThumbProps } from "reka-ui"
import type { HTMLAttributes } from "vue"
import { reactiveOmit } from "@vueuse/core"
import { SwitchThumb } from "reka-ui"
import { cn } from "@/lib/utils"

const props = defineProps<SwitchThumbProps & { class?: HTMLAttributes["class"] }>()
</script>
