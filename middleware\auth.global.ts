// Simple auth middleware - only protects routes that explicitly use it
export default defineNuxtRouteMiddleware((to, from) => {
  const { $isLoggedIn } = useNuxtApp()
  const publicRoutes: string[] = [
    '/',
    '/login',
    '/signup',
  ]

  const isPublicRoute = publicRoutes.some((route: string) => {return to.path === route || to.path.startsWith(`${route}/`) })

  const isAuthPage = [
    '/login',
    '/signup',
  ].includes(to.path)

  // Redirect authenticated users away from auth pages to dashboard
  if ($isLoggedIn.value && isAuthPage) {
    return navigateTo('/dashboard')
  }

  // Redirect unauthenticated users to login (except for public routes)
  if (!$isLoggedIn.value && !isPublicRoute) {
    return navigateTo('/login')
  }
})