@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-display: "Outfit", sans-serif;
  --font-limelight: "Limelight", cursive;

  /* colors */
  /* --color-*: initial; */
  --color-primary-100: #fef3c7; 
  --color-primary-200: #fde68a; 
  --color-primary-300: #fcd34d; 
  --color-primary-400: #fbbf24; 
  --color-primary-500: #f59e0b; 
  --color-primary-600: #d97706; 
  --color-primary-700: #b45309; 
  --color-primary-800: #EAB308; 
  --color-primary-800: #fff9f9; 

  --color-secondary-100: #a4c2ee;
  --color-secondary-200: #76a4e6;
  --color-secondary-300: #4985dd;
  --color-secondary-400: #2569cb;
  --color-secondary-500: #1d519d;
  --color-secondary-600: #153a70;
  --color-secondary-700: #0c2243;

  --color-tertiary-100: #ffedd5; 
  --color-tertiary-200: #fed7aa; 
  --color-tertiary-300: #fdba74; 
  --color-tertiary-400: #fb923c; 
  --color-tertiary-500: #f97316; 
  --color-tertiary-600: #ea580c; 

  --color-neutral-100: #ffffff;
  --color-neutral-150: #f2f2f2;
  --color-neutral-200: #e8e8e8;
  --color-neutral-300: #d2d2d2;
  --color-neutral-400: #bbbbbb;
  --color-neutral-500: #a4a4a4;
  --color-neutral-600: #8e8e8e;
  --color-neutral-700: #777777;
  --color-neutral-800: #606060;
  --color-neutral-900: #4a4a4a;
  --color-neutral-1000: #333333;

  --color-green-100: #c5ffc5;
  --color-green-200: #8cff8c;
  --color-green-300: #53ff53;
  --color-green-400: #19ff19;
  --color-green-500: #00e500;
  --color-green-600: #00b200;
  --color-green-700: #007f00;
  --color-green-800: #036856;

  --color-red-100: #fb3748;
  --color-red-200: #d00416;

  --color-black: #000;
  --color-black-200: #fafbfc;

  --color-white: #fff;
  --color-white-200: #FFFDFD;

  --color-dark: #0F172A;
  --color-purple: oklch(70.2% 0.183 293.541);
  

  /* typography */
  --text-h1: 4.25rem;
  --text-h1--line-height: 6.375rem;
  --text-h2: 3.5rem;
  --text-h2--line-height: 5.25rem;
  --text-h3: 2.875rem;
  --text-h3--line-height: 4.3125rem;
  --text-h4: 2.375rem;
  --text-h4--line-height: 3.5625rem;
  --text-h5: 2rem;
  --text-h5--line-height: 3rem;
  --text-h6: 1.625rem;
  --text-h6--line-height: 2.44rem;
  --text-h7: 1.375rem;
  --text-h7--line-height: 2rem;

  --text-body-1: 1.125rem;
  --text-body-1--line-height: 1.688rem;
  --text-body-2: 1rem;
  --text-body-2--line-height: 1.5rem;
  --text-body-3: 0.875rem;
  --text-body-3--line-height: 1.3125rem;
  --text-body-4: 0.75rem;
  --text-body-4--line-height: 1.125rem;
  --text-body-5: 0.625rem;
  --text-body-5--line-height: 0.9375rem;
}

@layer components {
  .text-h1,
  .text-h2,
  .text-h3,
  .text-h4,
  .text-h5,
  .text-h6,
  .text-h7,
  .text-body-1,
  .text-body-2,
  .text-body-3,
  .text-body-4,
  .text-body-5 {
    letter-spacing: var(--tracking-normal);
  }

  .text-h1,
  .text-h2,
  .text-h3,
  .text-h4,
  .text-h5,
  .text-h6,
  .text-h7 {
    font-weight: var(--font-weight-bold);
  }

  .text-body-1 {
    font-weight: var(--font-weight-medium);
  }

  .text-body-2,
  .text-body-3,
  .text-body-4,
  .text-body-5 {
    font-weight: var(--font-weight-normal);
  }

  .gt-primary {
    @apply 
      bg-gradient-to-r 
        from-[var(--gradient-primary-from)] 
        via-[var(--gradient-primary-via)] 
        to-[var(--gradient-primary-to)] 
        bg-clip-text 
        text-transparent;
  }

  .gt-secondary {
    background: linear-gradient(135deg, #fbbf24, #f97316);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .primary-background {
   @apply 
    bg-gradient-to-r
      from-[var(--gradient-primary-from)]
      via-[var(--gradient-primary-via)]
      to-[var(--gradient-primary-to)];
  }

 .secondary-background {
    @apply 
    bg-gradient-to-r
      from-[var(--gradient-secondary-from)]
      to-[var(--gradient-secondary-to)];
  }

  .tertiary-background{ 
    background: linear-gradient(
      to bottom right,
      oklch(90.57% 0.235 98.75),  
      oklch(75.2% 0.247 53.63)    
    );
  }

  /* scrollbars */
  ::-webkit-scrollbar {
    width: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #f1f5f9;
  }
  ::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #fbbf24, #f97316);
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #f59e0b, #ea580c);
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}


:root {
  --background: #fff9f9;
  --foreground: hsl(224 71.4% 4.1%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(224 71.4% 4.1%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(224 71.4% 4.1%);

  --primary: hsl(45 93% 58%);
  --primary-foreground: hsl(224 71.4% 4.1%);

  --secondary: hsl(220 13% 91%);
  --secondary-foreground: hsl(224 71.4% 4.1%);

  --muted: hsl(220 13% 91%);
  --muted-foreground: hsl(220 9% 46%);

  --accent: hsl(220 13% 91%);
  --accent-foreground: hsl(224 71.4% 4.1%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 20% 98%);

  --border: hsl(220 13% 91%);
  --input: hsl(220 13% 91%);
  --ring: hsl(45 93% 58%);

  --radius: 0.5rem;

  /* primary gradient stops */
  --gradient-primary-from: hsl(45 93% 58%);   
  --gradient-primary-via:  hsl(24 100% 57%);  
  --gradient-primary-to:   hsl(4 85% 63%);    

  /* secondary gradient stops */
  --gradient-secondary-from: hsl(45 93% 58%); 
  --gradient-secondary-to:   hsl(24 100% 57%);
}

.dark {
--background: hsl(222.2 84% 4.9%);
--foreground: hsl(210 40% 98%);
--card: hsl(222.2 84% 4.9%);
--card-foreground: hsl(210 40% 98%);
--popover: hsl(222.2 84% 4.9%);
--popover-foreground: hsl(210 40% 98%);

--primary: hsl(210 40% 98%);
--primary-foreground: hsl(222.2 47.4% 11.2%);

--secondary: hsl(217.2 32.6% 17.5%);
--secondary-foreground: hsl(210 40% 98%);

--muted: hsl(217.2 32.6% 17.5%);
--muted-foreground: hsl(215 20.2% 65.1%);

--accent: hsl(217.2 32.6% 17.5%);
--accent-foreground: hsl(210 40% 98%);

--destructive: hsl(0 62.8% 30.6%);
--destructive-foreground: hsl(210 40% 98%);

--border: hsl(217.2 32.6% 17.5%);
--input: hsl(217.2 32.6% 17.5%);
--ring: hsl(212.7 26.8% 83.9%);
}

@layer base {
  * {
   @apply border-border outline-ring/50 transition-colors duration-200 ease-in-out;
  }
  body {
    @apply bg-background text-foreground font-sans overflow-x-hidden;
  }
}
