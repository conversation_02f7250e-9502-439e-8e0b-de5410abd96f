<template>
  <div
    class="min-h-screen bg-gradient-to-br from-background via-neutral-150 to-neutral-200 relative overflow-hidden"
  >
    <div class="">
      <auth-animated-bg />
      <auth-floating-shapes />
    </div>

    <div class="relative z-10 min-h-screen flex flex-col lg:flex-row">
      <div
        class="lg:flex-1 lg:primary-background lg:relative lg:overflow-hidden lg:flex lg:items-center lg:justify-center p-4 lg:p-0"
      >
        <auth-login-showcase />

        <!-- Mobile: Header -->
        <div class="lg:hidden text-center animate-fade-in-up">
          <div
            class="inline-flex items-center justify-center w-24 h-24 bg-primary-900 border border-primary-900 rounded-3xl mb-2"
          >
            <img
              src="/public/favicon.png"
              alt="Bookiime Logo"
              class="w-12 h-12 object-contain"
            />
          </div>
          <p
            class="text-body-2 text-neutral-600 animate-fade-in"
            style="animation-delay: 0.3s"
          >
            Your scheduling revolution starts here
          </p>
        </div>
      </div>

      <div
        class="lg:flex-1 lg:bg-white lg:relative lg:flex lg:items-center lg:justify-center flex-1 flex items-center justify-center p-4"
      >
        <div class="w-full max-w-md">
          <!-- Desktop Header -->
          <div class="hidden lg:block text-center mb-8">
            <div
              class="inline-flex items-center justify-center w-16 h-16 bg-neutral-100 rounded-2xl mb-6"
            >
              <Icon name="lucide:layers" class="w-8 h-8 text-primary-600" />
            </div>
            <h2 class="text-h4 font-bold text-neutral-900 mb-2">
              Welcome Back!
            </h2>
            <p class="text-body-2 text-neutral-600">
              Sign in to access your booking dashboard
            </p>
          </div>

          <div
            class="lg:bg-transparent lg:backdrop-blur-none lg:shadow-none lg:border-none lg:p-0 bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 animate-fade-in-up"
            style="animation-delay: 0.5s"
          >
            <auth-login-form />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  public: true
})

useHead({
  title: 'Sign In - Bookiime',
  meta: [
    { name: 'description', content: 'Sign in to your Bookiime account and access your booking dashboard' }
  ]
})
</script>

<style scoped>
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}
</style>
