<template>
  <aside :class="sidebarClasses">

    <!-- Logo Section (Desktop Only) -->
    <div v-if="!isMobile" class="p-4 pt-8">
      <div class="flex items-center justify-between">
        <div class="flex-1 flex items-center justify-center">
          <NuxtLink to="/dashboard" class="flex items-center justify-center hover:scale-110 transition-transform duration-200">
            <img
              src="/favicon.png"
              alt="Bookiime"
              :class="[
                'h-10 md:h-12 w-auto',
                isCollapsed ? 'w-20 h-12' : 'w-28 h-16'
              ]"
              style="object-position: center; image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;"
            />
          </NuxtLink>
        </div>

        <!-- Collapse <PERSON><PERSON> (Desktop Only) -->
        <Button
          v-if="!isCollapsed"
          @click="$emit('toggle-collapse')"
          variant="ghost"
          size="sm"
          class="p-2 rounded-lg hover:bg-neutral-100 transition-colors flex-shrink-0"
        >
          <PanelLeftClose class="w-4 h-4 text-neutral-600" />
        </Button>
      </div>
    </div>

    <!-- Navigation Section - Dynamic Island -->
    <div class="flex-1 px-3 py-8 flex flex-col justify-center">
      <nav :class="[
        'backdrop-blur-xl rounded-3xl shadow-2xl border transition-all duration-500 ease-out transform hover:scale-[1.02]',
        'relative overflow-hidden',
        isCollapsed
          ? 'bg-neutral-100 border-neutral-200 px-2 py-3'
          : 'bg-neutral-50 border-neutral-200 px-3 py-4'
      ]">
        <!-- Dynamic Island Glow Effect -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-transparent to-primary-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        <!-- Navigation Items Container -->
        <div class="relative z-10 flex flex-col" :class="isCollapsed ? 'space-y-1' : 'space-y-2'">
          <dashboard-sidebar-item
            v-for="item in navigationItems"
            :key="item.route"
            :item="item"
            :collapsed="isCollapsed"
            @click="handleItemClick"
          />
        </div>

        <!-- Dynamic Island Shimmer Effect -->
        <div class="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-700">
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 animate-shimmer"></div>
        </div>
      </nav>
    </div>

    <!-- User Section (Desktop Only) -->
    <div v-if="!isMobile" class="p-3">
      <Card :class="[
        'shadow-sm border-neutral-200 transition-all duration-300',
        isCollapsed ? 'bg-white p-3' : 'bg-white p-4'
      ]">
        <CardContent class="p-0">
          <dashboard-sidebar-user
            :user="user"
            :collapsed="isCollapsed"
            @logout="$emit('logout')"
          />
        </CardContent>
      </Card>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { X, PanelLeftClose } from 'lucide-vue-next'
import { onUnmounted } from 'vue'

interface NavigationItem {
  icon: string
  label: string
  route: string
  active?: boolean
}

interface User {
  id?: string
  name?: string
  email?: string
  avatar?: string
}

interface Props {
  show?: boolean
  collapsed: boolean
  navigationItems: NavigationItem[]
  user: User | null
}

interface Emits {
  (e: 'close'): void
  (e: 'logout'): void
  (e: 'toggle-collapse'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false
})

const emit = defineEmits<Emits>()

// Reactive window width for mobile detection
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024)

// Update window width on resize
if (typeof window !== 'undefined') {
  const updateWidth = () => {
    windowWidth.value = window.innerWidth
  }
  window.addEventListener('resize', updateWidth)
  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth)
  })
}

// Computed properties for mobile detection and collapsed state
const isMobile = computed(() => {
  return windowWidth.value < 1024
})

const isCollapsed = computed(() => {
  return isMobile.value ? true : props.collapsed // Mobile always collapsed, desktop uses props
})

// Handle sidebar item click - consistent behavior across all screens
const handleItemClick = () => {
  if (isMobile.value) {
    // On mobile: close the sidebar completely
    emit('close')
  } else {
    // On desktop: collapse sidebar if expanded
    if (!props.collapsed) {
      emit('toggle-collapse')
    }
  }
}

// Computed classes for sidebar - consistent across all screens
const sidebarClasses = computed(() => {
  const baseClasses = 'fixed left-0 top-0 z-50 h-screen transition-all duration-300 flex flex-col'

  return [
    baseClasses,
    // Clean background without glassy effects
    'bg-white border-r border-neutral-200',
    // Width behavior - mobile always collapsed when shown
    isMobile.value ? 'w-20' : (props.collapsed ? 'w-20' : 'w-80'),
    // Show/hide behavior
    isMobile.value
      ? (props.show ? 'translate-x-0' : '-translate-x-full') // Mobile: show/hide based on props.show
      : 'translate-x-0' // Desktop: always visible
  ]
})
</script>

<style scoped>
@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Enhanced hover effects for navigation */
nav:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.4);
}

/* Dynamic Island breathing effect */
nav {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.005);
  }
}
</style>
