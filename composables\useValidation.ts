// Validation rule interface following Open/Closed Principle
export interface ValidationRule {
  validate(value: any): boolean
  message: string
}

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

// Field validation result
export interface FieldValidationResult {
  isValid: boolean
  error: string | null
}

// Abstract base validator class
export abstract class BaseValidator implements ValidationRule {
  abstract validate(value: any): boolean
  abstract message: string
}

// Email validation rule
export class EmailValidator extends BaseValidator {
  message = 'Please enter a valid email address'
  
  validate(value: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value)
  }
}

// Required field validation rule
export class RequiredValidator extends BaseValidator {
  message = 'This field is required'
  
  validate(value: any): boolean {
    if (typeof value === 'string') {
      return value.trim().length > 0
    }
    return value !== null && value !== undefined
  }
}

// Minimum length validation rule
export class MinLengthValidator extends BaseValidator {
  message: string
  
  constructor(private minLength: number) {
    super()
    this.message = `Must be at least ${minLength} characters long`
  }
  
  validate(value: string): boolean {
    return typeof value === 'string' && value.length >= this.minLength
  }
}

// Password strength validation rule
export class PasswordStrengthValidator extends BaseValidator {
  message = 'Password must contain uppercase, lowercase, and number'
  
  validate(value: string): boolean {
    const hasUppercase = /[A-Z]/.test(value)
    const hasLowercase = /[a-z]/.test(value)
    const hasNumber = /\d/.test(value)
    const hasMinLength = value.length >= 8
    
    return hasUppercase && hasLowercase && hasNumber && hasMinLength
  }
}

// Phone number validation rule
export class PhoneValidator extends BaseValidator {
  message = 'Please enter a valid phone number'
  
  validate(value: string): boolean {
    // Basic phone validation - can be extended
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    return phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))
  }
}

// Subdomain validation rule
export class SubdomainValidator extends BaseValidator {
  message = 'Invalid subdomain format'
  
  validate(value: string): boolean {
    if (value.length < 3) return false
    if (!/^[a-z0-9-]+$/.test(value)) return false
    if (value.startsWith('-') || value.endsWith('-')) return false
    
    // Check against reserved subdomains
    const reserved = ['admin', 'api', 'www', 'test', 'demo', 'mail', 'ftp']
    return !reserved.includes(value)
  }
}

// Field validator class that can hold multiple rules
export class FieldValidator {
  private rules: ValidationRule[] = []
  
  addRule(rule: ValidationRule): FieldValidator {
    this.rules.push(rule)
    return this
  }
  
  validate(value: any): FieldValidationResult {
    for (const rule of this.rules) {
      if (!rule.validate(value)) {
        return {
          isValid: false,
          error: rule.message
        }
      }
    }
    
    return {
      isValid: true,
      error: null
    }
  }
}

// Form validation service following Single Responsibility Principle
export class FormValidationService {
  private validators: Map<string, FieldValidator> = new Map()
  
  addField(fieldName: string, validator: FieldValidator): void {
    this.validators.set(fieldName, validator)
  }
  
  validateField(fieldName: string, value: any): FieldValidationResult {
    const validator = this.validators.get(fieldName)
    if (!validator) {
      return { isValid: true, error: null }
    }
    
    return validator.validate(value)
  }
  
  validateForm(formData: Record<string, any>): ValidationResult {
    const errors: string[] = []
    
    for (const [fieldName, validator] of this.validators) {
      const value = formData[fieldName]
      const result = validator.validate(value)
      
      if (!result.isValid && result.error) {
        errors.push(`${fieldName}: ${result.error}`)
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Composable for form validation
export const useFormValidation = () => {
  const validationService = new FormValidationService()
  
  // Create validators for common field types
  const createEmailValidator = () => {
    return new FieldValidator()
      .addRule(new RequiredValidator())
      .addRule(new EmailValidator())
  }
  
  const createPasswordValidator = () => {
    return new FieldValidator()
      .addRule(new RequiredValidator())
      .addRule(new MinLengthValidator(8))
      .addRule(new PasswordStrengthValidator())
  }
  
  const createPhoneValidator = () => {
    return new FieldValidator()
      .addRule(new RequiredValidator())
      .addRule(new PhoneValidator())
  }
  
  const createSubdomainValidator = () => {
    return new FieldValidator()
      .addRule(new RequiredValidator())
      .addRule(new MinLengthValidator(3))
      .addRule(new SubdomainValidator())
  }
  
  const createRequiredValidator = () => {
    return new FieldValidator()
      .addRule(new RequiredValidator())
  }
  
  return {
    validationService,
    createEmailValidator,
    createPasswordValidator,
    createPhoneValidator,
    createSubdomainValidator,
    createRequiredValidator,
    // Validator classes for custom use
    EmailValidator,
    RequiredValidator,
    MinLengthValidator,
    PasswordStrengthValidator,
    PhoneValidator,
    SubdomainValidator,
    FieldValidator
  }
}
